#!/usr/bin/env python3
"""
简化架构使用示例 - 展示如何使用去掉注意力机制的解耦学习框架
"""

import torch
import torch.nn as nn
from attention import DecoupledFusion, GraphMLMutiView
from models import DisentangledIBKD_GNN

def create_simplified_teacher_model(dataset, hidden_dim=128, num_view=4, latent_dim=64):
    """
    创建简化的教师模型 - 直接通过解耦学习产生教师编码 Z_φ
    
    Args:
        dataset: 图数据集
        hidden_dim: 隐藏层维度
        num_view: 视图数量
        latent_dim: 潜在空间维度
    
    Returns:
        teacher_model: 简化的教师模型
    """
    print("创建简化的教师模型...")
    print(f"  - 隐藏维度: {hidden_dim}")
    print(f"  - 视图数量: {num_view}")
    print(f"  - 潜在维度: {latent_dim}")
    
    # 使用简化的 GraphMLMutiView (去掉了 layer_num_heads 参数)
    teacher_model = GraphMLMutiView(
        dataset=dataset,
        hidden=hidden_dim,
        num_view=num_view,
        latent_dim_decoupled=latent_dim,
        num_feat_layers=1,
        num_conv_layers=3,
        num_fc_layers=2,
        residual=False,
        res_branch="BNConvReLU",
        global_pool="sum",
        dropout=0,
        edge_norm=True
    )
    
    print("  ✓ 教师模型创建成功")
    return teacher_model

def create_downstream_model(teacher_model, latent_dim=64, num_classes=10):
    """
    创建下游任务模型 - 使用教师编码进行分类
    
    Args:
        teacher_model: 教师模型
        latent_dim: 潜在空间维度
        num_classes: 分类类别数
    
    Returns:
        downstream_model: 下游任务模型
    """
    print("创建下游任务模型...")
    print(f"  - 潜在维度: {latent_dim}")
    print(f"  - 分类类别: {num_classes}")
    
    # 创建完整的解耦信息瓶颈知识蒸馏模型
    downstream_model = DisentangledIBKD_GNN(
        teacher_model=teacher_model,
        latent_dim=latent_dim,
        num_classes=num_classes,
        lambda_ib=1.0,    # 信息瓶颈权重
        lambda_r=1.0,     # 重建损失权重
        lambda_kd=1.0,    # 知识蒸馏权重
        lambda_orth=1.0   # 正交性约束权重
    )
    
    print("  ✓ 下游任务模型创建成功")
    return downstream_model

def demonstrate_forward_pass(model, dataset_lst):
    """
    演示前向传播过程
    
    Args:
        model: 模型
        dataset_lst: 多视图数据集列表
    
    Returns:
        outputs: 模型输出
    """
    print("\n演示前向传播过程...")
    
    # 设置为评估模式
    model.eval()
    
    with torch.no_grad():
        # 前向传播
        outputs = model(dataset_lst)
        
        print("输出组件:")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"  - {key}: shape {value.shape}")
            else:
                print(f"  - {key}: {type(value)}")
        
        # 关键输出解释
        print("\n关键输出解释:")
        print(f"  - teacher_encoding: 教师编码 Z_φ，shape {outputs['teacher_encoding'].shape}")
        print(f"  - z_vs: 非冗余特征，用于分类，shape {outputs['z_vs'].shape}")
        print(f"  - z_vr: 冗余特征，需要被抑制，shape {outputs['z_vr'].shape}")
        print(f"  - logits: 分类输出，shape {outputs['logits'].shape}")
    
    return outputs

def demonstrate_architecture_flow():
    """
    演示简化架构的数据流
    """
    print("\n" + "="*60)
    print("简化架构数据流演示")
    print("="*60)
    
    print("\n架构流程:")
    print("1. 多视图图数据 → 视图特定GCN编码器")
    print("2. 各视图编码 → 解耦融合机制 (DecoupledFusion)")
    print("3. 解耦融合 → 教师编码 Z_φ (去掉注意力机制)")
    print("4. 教师编码 Z_φ → 学生网络分离 (z_vs, z_vr)")
    print("5. 非冗余特征 z_vs → 下游分类任务")
    
    print("\n核心改进:")
    print("✅ 去掉了冗余的注意力机制")
    print("✅ 直接通过解耦学习产生教师编码")
    print("✅ 简化了计算流程，提高效率")
    print("✅ 保留了所有理论框架功能")
    
    print("\n数学公式:")
    print("- 多视图编码: Z_φ = Θ_Φ X_Φ (可学习融合权重)")
    print("- 信息瓶颈: L_IBT = -I(y, z_φ) + β_T × I(z_φ, x)")
    print("- 学生损失: L_IBS = L_Near_OPT + I(z_vr; z_vs)")
    print("- 正交约束: HSIC(z_vs, z_vr) → 0")

def main():
    """
    主函数 - 完整的使用示例
    """
    print("="*60)
    print("简化解耦架构使用示例")
    print("="*60)
    
    # 模拟参数
    batch_size = 32
    num_view = 4
    feature_dim = 128
    latent_dim = 64
    num_classes = 10
    
    print(f"\n模拟参数:")
    print(f"  - 批次大小: {batch_size}")
    print(f"  - 视图数量: {num_view}")
    print(f"  - 特征维度: {feature_dim}")
    print(f"  - 潜在维度: {latent_dim}")
    print(f"  - 分类类别: {num_classes}")
    
    # 创建模拟数据集
    print(f"\n创建模拟数据...")
    
    # 模拟图数据集 (简化版本)
    class MockDataset:
        def __init__(self):
            self.num_features = feature_dim
            self.num_classes = num_classes
    
    dataset = MockDataset()
    
    # 模拟多视图数据
    dataset_lst = []
    for i in range(num_view):
        # 每个视图的模拟数据
        class MockData:
            def __init__(self):
                self.x = torch.randn(batch_size * 10, feature_dim)  # 节点特征
                self.batch = torch.repeat_interleave(torch.arange(batch_size), 10)  # 批次索引
                self.y = torch.randint(0, num_classes, (batch_size,))  # 标签
        
        dataset_lst.append(MockData())
    
    print(f"  ✓ 创建了 {len(dataset_lst)} 个视图的模拟数据")
    
    try:
        # 步骤1: 创建教师模型
        teacher_model = create_simplified_teacher_model(
            dataset=dataset,
            hidden_dim=feature_dim,
            num_view=num_view,
            latent_dim=latent_dim
        )
        
        # 步骤2: 创建下游任务模型
        downstream_model = create_downstream_model(
            teacher_model=teacher_model,
            latent_dim=latent_dim,
            num_classes=num_classes
        )
        
        # 步骤3: 演示前向传播
        outputs = demonstrate_forward_pass(downstream_model, dataset_lst)
        
        # 步骤4: 演示架构流程
        demonstrate_architecture_flow()
        
        print("\n" + "="*60)
        print("🎉 简化架构演示成功完成！")
        print("="*60)
        
        print("\n使用建议:")
        print("1. 在 DMG_pretrain_main.py 中调用简化的 GraphMLMutiView")
        print("2. 去掉 layer_num_heads 参数")
        print("3. 直接使用教师编码 Z_φ 进行下游任务")
        print("4. 调整超参数: beta_t, beta_s, gamma_s, kappa, R")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查模型实现和数据格式")

if __name__ == "__main__":
    main()
