import numpy as np
import torch
from torch import tensor
import sys, os


class ConfidenceEstmator():
    def __init__(self, dim_catagory):
        self.C_yhat_ystar = np.array([[0 for i in range(dim_catagory)] for j in range(dim_catagory)])
        self.Q_yhat_ystar = np.array([[0 for i in range(dim_catagory)] for j in range(dim_catagory)])
        self.threshold = np.array([0.0 for i in range(dim_catagory)])
        self.matrix_hat_star = np.array([[0 for i in range(dim_catagory)] for j in range(dim_catagory)])
        self.X_hat2star = np.array([[0 for i in range(dim_catagory)] for j in range(dim_catagory)])
        self.X_hat_hat2star = np.array([[1e-6 for i in range(dim_catagory)] for j in range(dim_catagory)])
        # self.X_hat2_total_cnt = np.array([0 for i in range(dim_catagory)])
        # self.C_hat2star = np.array([[0 for i in range(dim_catagory)] for j in range(dim_catagory)])
        self.Q_hat_hat2star = np.array([[0.0 for i in range(dim_catagory)] for j in range(dim_catagory)])
        self.dim_catagory = dim_catagory

    def compute_weights(self, pretrain_check_size, batch_size, data_origin, list_mutiview_out, model_parameter, mutiview_model_parameter, class_dim):
        # update parameters
        new_results = self.check_compute_results(pretrain_check_size, batch_size, data_origin, list_mutiview_out, model_parameter, mutiview_model_parameter, class_dim)

        matrix_smooth_factor = np.array(
            [[0.000001 for i in range(self.dim_catagory)] for j in range(self.dim_catagory)])
        self.X_hat2star = self.X_hat2star + new_results

        self.X_y_hat = np.sum(self.X_hat2star, axis=1)
        dominitor_X_hat2star = np.sum(self.X_hat2star)
        matrix_dominitor_X_y_hat2_y_star = np.array(
            [[dominitor_X_hat2star for i in range(self.dim_catagory)] for j in range(self.dim_catagory)])
        p_y_hat2y_star = self.X_hat2star / matrix_dominitor_X_y_hat2_y_star + matrix_smooth_factor
        tmp_p_y_hat2y_star = p_y_hat2y_star
        # np.expand_dims(tmp_p_y_hat2y_star, 0).repeat(self.dim_catagory, axis=0)
        # 1 * dim
        X_y_star = np.sum(self.X_hat2star, axis=0)
        p_y_star = X_y_star / dominitor_X_hat2star
        #  dim * dim
        p_y_star = np.expand_dims(p_y_star, 0).repeat(self.dim_catagory, axis=0)
        p_y_star = p_y_star + matrix_smooth_factor
        p_y_hat_cond_y_star = p_y_hat2y_star / p_y_star
        X_y_hat = np.sum(self.X_hat2star, axis=1)
        p_y_hat = X_y_hat / dominitor_X_hat2star
        p_y_hat = np.expand_dims(p_y_hat, 1).repeat(self.dim_catagory, axis=1)
        p_y_hat = p_y_hat + matrix_smooth_factor
        p_y_star_cond_y_hat_cond = p_y_hat2y_star / p_y_hat
        p_y_star_cond_y_hat_cond = np.transpose(p_y_star_cond_y_hat_cond)
        t_matrix = np.dot(p_y_hat_cond_y_star, p_y_star_cond_y_hat_cond)
        for i in range(self.dim_catagory):
            self.threshold[i] = t_matrix[i][i]
        sys.stdout = open(os.devnull, 'w')
        out = model_parameter(data_origin)
        sys.stdout = sys.__stdout__
        out = torch.exp(out)
        Q_hat_hat2star = self.confidence_score(pretrain_check_size, batch_size, data_origin, out)
        return Q_hat_hat2star

    def check_compute_results(self, pretrain_check_size, batch_size, data_origin, list_mutiview_out, model_parameter, mutiview_model_parameter, class_dim):
        if pretrain_check_size >= batch_size:
            print(pretrain_check_size + "number error!")
        sys.stdout = open(os.devnull, 'w')

        out_original = model_parameter(data_origin)
        sys.stdout = sys.__stdout__

        out_select = out_original[:pretrain_check_size, :]
        pred = out_select.max(1)[1]
        label_select = data_origin.y.view(-1)[:pretrain_check_size]

        new_results = np.array([[0 for i in range(self.dim_catagory)] for j in range(self.dim_catagory)])
        dim_check_size = list(out_select.shape)[0]

        for i in range(dim_check_size):
            if pred[i] == label_select[i]:
                new_results[pred[i]][pred[i]] = new_results[pred[i]][pred[i]] + 1
            else:
                index_hat = pred[i]
                index_star = label_select[i]
                new_results[index_hat][index_star] = new_results[index_hat][index_star] + 1
        return new_results

    def confidence_score(self, pretrain_check_size, batch_size, data, out):
        # probability = np.array([0.0 for i in range(pretrain_check_size)])
        label = data.y[0:pretrain_check_size]
        dim_check_size = list(label.shape)[0]

        prodict_probability = out.max(1)[0]
        # for i in range(pretrain_check_size):
        #     probability[i] = out[i, label[i]]

        if pretrain_check_size > batch_size:
            print("pretrain_check_size bigger than batch_size error")
        else:
            for i in range(dim_check_size):
                index_threshold = label[i]
                threshhold = self.threshold[index_threshold]
                if self.threshold[index_threshold] <= prodict_probability[i]:
                    self.X_hat_hat2star[label[i]][label[i]] = self.X_hat_hat2star[label[i]][label[i]] + 1
                else:
                    index_j = 0
                    if label[i] == 0:
                        index_j = 1
                    self.X_hat_hat2star[index_j][label[i]] = self.X_hat_hat2star[index_j][label[i]] + 1

        C_hat2star = self.X_hat_hat2star
        dominiator_C_hat2star = np.sum(C_hat2star, axis=1)

        tmp_Q_hat_hat2star = np.array([[0.0 for i in range(self.dim_catagory)] for j in range(self.dim_catagory)])
        for i in range(self.dim_catagory):
            for j in range(self.dim_catagory):
                tmp_Q_hat_hat2star[i][j] = (C_hat2star[i][j] / dominiator_C_hat2star[i] ) * self.X_y_hat[i]
        dominiator_tmp_Q_hat_hat2star = np.sum(tmp_Q_hat_hat2star)
        for i in range(self.dim_catagory):
            for j in range(self.dim_catagory):
                self.Q_hat_hat2star[i][j] = tmp_Q_hat_hat2star[i][j] / dominiator_tmp_Q_hat_hat2star
        return self.Q_hat_hat2star
