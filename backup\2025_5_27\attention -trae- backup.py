import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import Linear, BatchNorm1d
from res_gcn import ResGCN
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# class GraphMLModel(torch.nn.Module): # 图ML模型类（未使用）
#     def __init__(self,num_view, num_head, in_size, hidden_size): # 初始化方法
#         super(GraphMLModel, self).__init__() # 调用父类初始化
#     def forward(self, z): # 前向传播方法 # 前向传播方法
#         print("123") # 打印信息

class GraphMLMutiView(torch.nn.Module):
    # def __init__(self, dataset, resgcn, hidden, layer_num_heads, num_view=4,num_feat_layers=1, num_conv_layers=3, num_fc_layers=2, residual=False, res_branch="BNConvReLU", global_pool="sum", dropout=0,edge_norm=True): # 另一种初始化方法签名（未使用）
    def __init__(self, dataset, hidden, layer_num_heads, num_view, num_feat_layers=1, num_conv_layers=3, num_fc_layers=2, residual=False, res_branch="BNConvReLU", global_pool="sum", dropout=0,edge_norm=True):

        super(GraphMLMutiView, self).__init__()
        self.gat_layers = nn.ModuleList()
        for i in range(num_view):
            self.gat_layers.append(ResGCN(dataset, hidden, num_feat_layers, num_conv_layers, num_fc_layers, gfn=False, collapse=False,
                          residual=residual, res_branch=res_branch,
                          global_pool=global_pool, dropout=dropout,
        # 假设 'hidden' 是GCN处理后输入到DecoupledLearning的特征维度。
        # 您可能需要根据gat_layers的输出调整DecoupledLearning的'in_size'。
        # 目前，我们假设self.gat_layers[i].forward_cl(data_i)的输出大小为'hidden'。
        # 如果gat_layers输出D*K，那么DecoupledLearning的in_size应该是D*K。
        # 我们需要确定ResGCN的实际输出维度，或者暂时假设它是'hidden'。
        # 为简单起见，我将使用'hidden'作为in_size。这需要根据ResGCN的输出维度进行验证。
        self.decoupled_learning = DecoupledLearning(in_size=hidden, hidden_size=hidden//2, latent_dim=hidden//4) # 示例大小
    def forward(self, dataset_lst):
        semantic_embeddings = []
        for i, data_i in enumerate(dataset_lst):
            semantic_embeddings.append(self.gat_layers[i].forward_cl(data_i))
        semantic_embeddings = torch.stack(semantic_embeddings, dim=1)  # (N, M, D * K) 或 (N, 视图数量, 特征维度)
        # DecoupledLearning的前向传播现在返回两个值。
        weighted_sum_embeddings, reconstructed_embeddings = self.decoupled_learning(semantic_embeddings) # 加权和嵌入，重构嵌入
        # 根据任务的不同，您可能希望返回一个或两个值。
        # 对于典型的对比学习设置，您可能会使用weighted_sum_embeddings。
        # reconstructed_embeddings用于计算重构损失。
        return weighted_sum_embeddings, reconstructed_embeddings

    def loss_cl(self, x1, x2):
        T = 0.5
        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        # tmp_dim = torch.einsum('i,j->ij', x1_abs, x2_abs) # 临时维度（未使用）
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.exp(sim_matrix / T)
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        loss = pos_sim / (sim_matrix.sum(dim=1) - pos_sim)
        loss = - torch.log(loss).mean()
        return loss
 
      def loss_cross_dm_ml(self, x1, x2, pos_weights, epoch, index_data_traverse,                 pretrain_check_size, data, carlibrator_switch):
        T = 0.5

        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        # tmp_dim = torch.einsum('i,j->ij', x1_abs, x2_abs) # 临时维度（未使用）
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = sim_matrix / T
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        # loss = torch.exp(confidence * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim) # 带置信度的损失计算（未使用）
        if epoch == 1 and index_data_traverse == 0:
            loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
            # loss_mark = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim) # 标记损失（未使用） # 标记损失（未使用）
        else:
            label_arr = data.y[:pretrain_check_size]
            batch_weight = np.array([1.0 for i in range(batch_size)])
            dim_check_size = list(label_arr.shape)[0]
            for i in range(dim_check_size):
                label_index = label_arr[i]
                batch_weight[i] = pos_weights[label_index][label_index]
            batch_weight = torch.from_numpy(batch_weight).to(device)
            # tmp = batch_weight * pos_sim # 临时变量（未使用）
            if carlibrator_switch:
                loss = torch.exp(batch_weight * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)
            else:
                loss = torch.exp(batch_weight) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)



        loss = - torch.log(loss + 1).mean()
        # loss2 = - torch.log(loss_mark + 1).mean() # 第二损失（未使用）

        return loss
 
      def loss_ml(self, x1, x2, pos_weights, epoch, index_data_traverse,                 pretrain_check_size, data, carlibrator_switch):
        T = 0.5

        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        tmp_dim = torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = sim_matrix / T
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        # loss = torch.exp(confidence * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim) # 带置信度的损失计算（未使用）
        if epoch == 1 and index_data_traverse == 0:
            loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
            loss_mark = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        else:
            label_arr = data.y[:pretrain_check_size]
            batch_weight = np.array([1.0 for i in range(batch_size)])
            dim_check_size = list(label_arr.shape)[0]
            for i in range(dim_check_size):
                label_index = label_arr[i]
                batch_weight[i] = pos_weights[label_index][label_index]
            batch_weight = torch.from_numpy(batch_weight).to(device)
            # tmp = batch_weight * pos_sim # 临时变量（未使用）
            if carlibrator_switch:
                loss = torch.exp(batch_weight * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)
            else:
                loss = torch.exp(batch_weight) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)

            # loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim) # 原始损失计算（未使用）
            # loss_mark = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim) # 标记损失（未使用） # 标记损失（未使用）
        # loss = - torch.log(loss + 1.0).mean() # 另一种损失计算（未使用）
        # tmp_loss_mark = loss_mark + 1 # 临时标记损失（未使用）

        loss = - torch.log(loss + 1).mean()
        # loss2 = - torch.log(loss_mark + 1).mean() # 第二损失（未使用）

        return loss

    def loss_reconstruction(self, original, reconstructed): # 重构损失函数
        # 常见的重构损失：均方误差
        loss = F.mse_loss(reconstructed, original)
        return loss



class DecoupledLearning(torch.nn.Module): # 解耦学习模块
    def __init__(self, in_size, hidden_size=128, latent_dim=64):  # 为自编码器添加latent_dim（潜在维度）
        super(DecoupledLearning, self).__init__() # 调用父类初始化
        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(in_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, latent_dim)
        )
        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, in_size)  # 输出维度应与输入匹配以进行重构
        )
        # 原始的注意力投影层（可以保留或根据您希望如何与自编码器结合进行修改）
        # 此投影用于在自编码之前计算组合多视图嵌入的权重
        self.attention_project = nn.Sequential(
            nn.Linear(in_size, hidden_size),
            nn.Tanh(),
            nn.Linear(hidden_size, 1, bias=False)
        )
    def forward(self, z): # 前向传播方法
        # z: (N, M, D * K) - 假设M是视图/模态的数量

        # 如果需要，重塑z以适应编码器。假设编码器首先独立处理每个视图。
        # 或者，如果您想编码组合表示，请相应调整。
        # 在此示例中，我们假设我们希望首先获得加权和，然后进行编码/解码。

        # 原始注意力机制部分（用于在自编码前对视图进行加权）
        # 这部分计算多视图嵌入的加权和
        # 如果M是视图数量，则z是 (N, 视图数量, 特征维度)
        if z.dim() == 3 and z.size(1) > 1: # 多视图输入
            w = self.attention_project(z).mean(0)  # (M, 1) -> (视图数量, 1)
            beta = torch.softmax(w, dim=0)  # (视图数量, 1)
            beta = beta.expand((z.shape[0],) + beta.shape)  # (N, 视图数量, 1)
            weighted_z = (beta * z).sum(1)  # (N, D * K) -> (N, 特征维度)
        elif z.dim() == 2: # 单视图输入或已聚合
            weighted_z = z # (N, 特征维度)
        else:
            raise ValueError(f"输入z具有意外的维度: {z.shape}")

        # 解耦学习部分（自编码器）
        latent_representation = self.encoder(weighted_z)  # (N, 潜在维度)
        reconstructed_z = self.decoder(latent_representation)  # (N, 特征维度)

        # 此模块的输出可以是潜在表示、重构结果，
        # 或原始的weighted_z，具体取决于下游任务。
        # 目前，我们返回weighted_z（如原始注意力机制中那样）
        # 并且可以使用reconstructed_z和weighted_z分别计算重构损失。
        return weighted_z, reconstructed_z # 为灵活性返回两者`

