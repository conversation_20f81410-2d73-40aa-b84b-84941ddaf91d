import torch
import torch.nn as nn
import torch.nn.functional as F

def rbf_kernel(x, y, sigma=1.0):
    """Compute the RBF kernel between two tensors."""
    dist_sq = torch.cdist(x, y, p=2)**2
    return torch.exp(-dist_sq / (2 * sigma**2))

class HSIC(nn.Module):
    """Hilbert-Schmidt Independence Criterion"""
    def __init__(self):
        super(HSIC, self).__init__()

    def forward(self, z_vs, z_vr):
        N = z_vs.size(0)
        if N < 2:
            return torch.tensor(0.0, device=z_vs.device)

        H = torch.eye(N, device=z_vs.device) - 1.0 / N

        K_vs = rbf_kernel(z_vs, z_vs)
        K_vr = rbf_kernel(z_vr, z_vr)

        K_vs_c = K_vs @ H
        K_vr_c = K_vr @ H

        hsic = torch.trace(K_vs_c @ K_vr_c)
        return hsic / ((N - 1)**2)

class Student(nn.Module):
    """Student network to produce specific (vs) or shared (vr) latent variables."""
    def __init__(self, input_dim, output_dim, hidden_dim=128):
        super(Student, self).__init__()
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        # These layers will output the parameters of the Gaussian distribution
        self.fc_mu = nn.Linear(hidden_dim, output_dim)
        self.fc_logvar = nn.Linear(hidden_dim, output_dim)
        self.relu = nn.ReLU()

    def forward(self, x):
        h = self.relu(self.fc1(x))
        h = self.relu(self.fc2(h))
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar

class Classifier(nn.Module):
    """A simple classifier head."""
    def __init__(self, input_dim, num_classes):
        super(Classifier, self).__init__()
        self.layer = nn.Linear(input_dim, num_classes)

    def forward(self, x):
        return self.layer(x)


class DisentangledIBKD_GNN(nn.Module):
    def __init__(self, teacher_model, latent_dim, num_classes, lambda_ib, lambda_r, lambda_kd, lambda_orth):
        super(DisentangledIBKD_GNN, self).__init__()
        self.teacher = teacher_model
        self.latent_dim = latent_dim

        # Student networks
        self.student_vs = Student(input_dim=latent_dim, output_dim=latent_dim)
        self.student_vr = Student(input_dim=latent_dim, output_dim=latent_dim)

        # Classifier head
        self.classifier = Classifier(input_dim=latent_dim, num_classes=num_classes)

        # Loss modules
        self.hsic = HSIC()
        self.reconstruction_loss_fn = nn.MSELoss()
        self.task_loss_fn = nn.CrossEntropyLoss()

        # Loss weights
        self.lambda_ib = lambda_ib
        self.lambda_r = lambda_r
        self.lambda_kd = lambda_kd
        self.lambda_orth = lambda_orth

    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def forward(self, dataset_lst):
        # Teacher forward pass
        fused_mu_teacher, fused_logvar_teacher, reconstructed_views, _, original_views = self.teacher(dataset_lst)
        z_teacher = self.reparameterize(fused_mu_teacher, fused_logvar_teacher)

        # Student forward pass
        mu_vs, logvar_vs = self.student_vs(z_teacher)
        mu_vr, logvar_vr = self.student_vr(z_teacher)

        z_vs = self.reparameterize(mu_vs, logvar_vs)
        z_vr = self.reparameterize(mu_vr, logvar_vr)

        # Classification
        logits = self.classifier(z_vs)

        return {
            'logits': logits,
            'reconstructed_views': reconstructed_views,
            'original_views': original_views,
            'fused_mu_teacher': fused_mu_teacher,
            'fused_logvar_teacher': fused_logvar_teacher,
            'mu_vs': mu_vs, 'logvar_vs': logvar_vs, 'z_vs': z_vs,
            'mu_vr': mu_vr, 'logvar_vr': logvar_vr, 'z_vr': z_vr,
            'z_teacher': z_teacher
        }

    def loss_function(self, outputs, labels, beta_t=1.0, beta_s=1.0, gamma_s=1.0):
        # 1. Task Loss (L_Task)
        loss_task = self.task_loss_fn(outputs['logits'], labels)

        # 2. Reconstruction Loss (L_R)
        loss_r = self.reconstruction_loss_fn(outputs['reconstructed_views'], outputs['original_views'])

        # 3. Orthogonality Loss (L_Orth)
        loss_orth = self.hsic(outputs['z_vs'], outputs['z_vr'])

        # 4. Information Bottleneck Loss (L_IB)
        def kl_divergence(mu, logvar):
            return -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp(), dim=1).mean()

        kl_teacher = kl_divergence(outputs['fused_mu_teacher'], outputs['fused_logvar_teacher'])
        kl_student_vs = kl_divergence(outputs['mu_vs'], outputs['logvar_vs'])
        kl_student_vr = kl_divergence(outputs['mu_vr'], outputs['logvar_vr'])
        
        # Simplified L_IB based on VIB principles
        loss_ibt = -loss_task + beta_t * kl_teacher # Approximation of -I(y, z_phi) + beta_T * I(z_phi, x)
        loss_ibs = kl_student_vs + kl_student_vr # Approximation of student IB terms
        loss_ib = loss_ibt + loss_ibs

        # 5. Knowledge Distillation Loss (L_KD)
        def contrastive_loss(z_student, z_teacher, tau=0.5):
            sim = F.cosine_similarity(z_student.unsqueeze(1), z_teacher.unsqueeze(0), dim=-1) / tau
            labels = torch.arange(z_student.size(0), device=z_student.device)
            return self.task_loss_fn(sim, labels)
        
        loss_ckd_vs = contrastive_loss(outputs['z_vs'], outputs['z_teacher'])
        loss_ckd_vr = contrastive_loss(outputs['z_vr'], outputs['z_teacher'])
        loss_kd = loss_ckd_vs + loss_ckd_vr

        # Total Loss
        total_loss = loss_task + \
                     self.lambda_r * loss_r + \
                     self.lambda_ib * loss_ib + \
                     self.lambda_kd * loss_kd + \
                     self.lambda_orth * loss_orth

        return {
            'total_loss': total_loss,
            'loss_task': loss_task,
            'loss_r': loss_r,
            'loss_ib': loss_ib,
            'loss_kd': loss_kd,
            'loss_orth': loss_orth
        }
