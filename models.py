import torch
import torch.nn as nn
import torch.nn.functional as F

def rbf_kernel(x, y, sigma=1.0):
    """Compute the RBF kernel between two tensors."""
    dist_sq = torch.cdist(x, y, p=2)**2
    return torch.exp(-dist_sq / (2 * sigma**2))

class HSIC(nn.Module):
    """
    Hilbert-Schmidt Independence Criterion for measuring independence between features.
    HSIC(Z_vs, Z_vr) = (1/(N-1)^2) * tr(K_vs * H * K_vr * H)
    """
    def __init__(self, sigma=1.0):
        super(HSIC, self).__init__()
        self.sigma = sigma

    def forward(self, z_vs, z_vr):
        """
        Compute HSIC between non-redundant (z_vs) and redundant (z_vr) features.
        Args:
            z_vs: Non-redundant features (batch_size, latent_dim)
            z_vr: Redundant features (batch_size, latent_dim)
        Returns:
            HSIC value (scalar)
        """
        batch_size = z_vs.size(0)
        if batch_size < 2:
            return torch.tensor(0.0, device=z_vs.device, requires_grad=True)

        # Centering matrix H = I - (1/N) * 1 * 1^T
        H = torch.eye(batch_size, device=z_vs.device) - torch.ones(batch_size, batch_size, device=z_vs.device) / batch_size

        # Compute RBF kernels
        K_vs = rbf_kernel(z_vs, z_vs, self.sigma)
        K_vr = rbf_kernel(z_vr, z_vr, self.sigma)

        # Center the kernels
        K_vs_centered = H @ K_vs @ H
        K_vr_centered = H @ K_vr @ H

        # Compute HSIC: tr(K_vs_centered * K_vr_centered)
        hsic_value = torch.trace(K_vs_centered @ K_vr_centered)

        # Normalize by (N-1)^2
        normalized_hsic = hsic_value / ((batch_size - 1) ** 2)

        return normalized_hsic

class Student(nn.Module):
    """Student network to produce specific (vs) or redundant (vr) latent variables through distillation."""
    def __init__(self, input_dim, output_dim, hidden_dim=128, student_type='vs'):
        super(Student, self).__init__()
        self.student_type = student_type  # 'vs' for non-redundant, 'vr' for redundant
        self.input_dim = input_dim
        self.output_dim = output_dim

        # Enhanced architecture for better distillation
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )

        # Separate heads for mean and log variance
        self.fc_mu = nn.Linear(hidden_dim, output_dim)
        self.fc_logvar = nn.Linear(hidden_dim, output_dim)

        # Additional projection layer for distillation alignment
        self.distill_proj = nn.Linear(output_dim, output_dim)

    def forward(self, z_teacher):
        """
        Forward pass for student network.
        Args:
            z_teacher: Teacher's fused representation z_φ
        Returns:
            mu, logvar: Parameters of the student's latent distribution
        """
        h = self.encoder(z_teacher)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)

        # Apply distillation projection
        mu_proj = self.distill_proj(mu)

        return mu_proj, logvar

class Classifier(nn.Module):
    """A simple classifier head."""
    def __init__(self, input_dim, num_classes):
        super(Classifier, self).__init__()
        self.layer = nn.Linear(input_dim, num_classes)

    def forward(self, x):
        return self.layer(x)


class DisentangledIBKD_GNN(nn.Module):
    def __init__(self, teacher_model, latent_dim, num_classes, lambda_ib, lambda_r, lambda_kd, lambda_orth):
        super(DisentangledIBKD_GNN, self).__init__()
        self.teacher = teacher_model  # GraphMLMutiView - 产生教师编码 Z_φ
        self.latent_dim = latent_dim
        self.num_classes = num_classes

        # Student networks for distillation learning
        # z_vs: non-redundant (specific) features - 用于下游分类任务
        # z_vr: redundant features - 需要被抑制的冗余信息
        self.student_vs = Student(input_dim=latent_dim, output_dim=latent_dim, student_type='vs')
        self.student_vr = Student(input_dim=latent_dim, output_dim=latent_dim, student_type='vr')

        # Classifier head - 直接使用非冗余特征 z_vs 进行分类
        self.classifier = Classifier(input_dim=latent_dim, num_classes=num_classes)

        # Loss modules
        self.hsic = HSIC()
        self.reconstruction_loss_fn = nn.MSELoss()
        self.task_loss_fn = nn.CrossEntropyLoss()

        # Loss weights
        self.lambda_ib = lambda_ib
        self.lambda_r = lambda_r
        self.lambda_kd = lambda_kd
        self.lambda_orth = lambda_orth

    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def forward(self, dataset_lst):
        # 获取教师编码 Z_φ - 通过解耦学习直接产生，无需注意力机制
        teacher_encoding, teacher_encoding_mu, teacher_encoding_logvar, reconstructed_views, individual_dist_params, original_views = self.teacher(dataset_lst)

        # Student networks 从教师编码 Z_φ 中学习分离特征
        # z_vs: 非冗余特征 (用于下游分类任务)
        # z_vr: 冗余特征 (需要被抑制)
        mu_vs, logvar_vs = self.student_vs(teacher_encoding)
        mu_vr, logvar_vr = self.student_vr(teacher_encoding)

        z_vs = self.reparameterize(mu_vs, logvar_vs)
        z_vr = self.reparameterize(mu_vr, logvar_vr)

        # 下游分类任务：直接使用非冗余特征 z_vs
        logits = self.classifier(z_vs)

        return {
            'logits': logits,
            'reconstructed_views': reconstructed_views,
            'original_views': original_views,
            'teacher_encoding': teacher_encoding,  # Z_φ
            'fused_mu_teacher': teacher_encoding_mu,
            'fused_logvar_teacher': teacher_encoding_logvar,
            'mu_vs': mu_vs, 'logvar_vs': logvar_vs, 'z_vs': z_vs,
            'mu_vr': mu_vr, 'logvar_vr': logvar_vr, 'z_vr': z_vr,
            'z_teacher': teacher_encoding  # 保持兼容性
        }

    def loss_function(self, outputs, labels, beta_t=1.0, beta_s=1.0, gamma_s=1.0, kappa=1.0, R=0.0):
        # 1. Task Loss (L_Task)
        loss_task = self.task_loss_fn(outputs['logits'], labels)

        # 2. Reconstruction Loss (L_R)
        loss_r = self.reconstruction_loss_fn(outputs['reconstructed_views'], outputs['original_views'])

        # 3. Orthogonality Loss (L_Orth) using HSIC
        loss_orth = self.hsic(outputs['z_vs'], outputs['z_vr'])

        # 4. Information Bottleneck Loss (L_IB) according to paper formulation
        def kl_divergence(mu, logvar):
            return -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp(), dim=1).mean()

        # Teacher model IB loss: L_IBT = -I(y, z_φ) + β_T × I(z_φ, x)
        # Approximated as: -task_loss + β_T × KL(q(z_φ)||p(z))
        kl_teacher = kl_divergence(outputs['fused_mu_teacher'], outputs['fused_logvar_teacher'])
        loss_ibt = -loss_task + beta_t * kl_teacher

        # Student model IB loss: L_IBS = L_Near_OPT + I(z_vr; z_vs)
        kl_student_vs = kl_divergence(outputs['mu_vs'], outputs['logvar_vs'])
        kl_student_vr = kl_divergence(outputs['mu_vr'], outputs['logvar_vr'])

        # L_Near_OPT = -κ × I(y; z_vs) + β_S × I(y; z_vr) + γ_S × (I(z_vr, Φ | y) - R)
        # Approximated using task loss and KL divergences
        vs_task_loss = F.cross_entropy(self.classifier(outputs['z_vs']), labels)
        vr_task_loss = F.cross_entropy(self.classifier(outputs['z_vr']), labels)

        loss_near_opt = (-kappa * (-vs_task_loss) +
                        beta_s * (-vr_task_loss) +
                        gamma_s * (kl_student_vr - R))

        # Mutual information between z_vr and z_vs (approximated by HSIC)
        mi_vr_vs = self.hsic(outputs['z_vr'], outputs['z_vs'])

        loss_ibs = loss_near_opt + mi_vr_vs
        loss_ib = loss_ibt + loss_ibs

        # 5. Knowledge Distillation Loss (L_KD) - Contrastive Knowledge Distillation
        def contrastive_loss(z_student, z_teacher, tau=0.5):
            # L_CKD = -E[log(exp(sim(z_s, z_t)/τ) / Σ_j exp(sim(z_s, z_j)/τ))]
            sim_matrix = F.cosine_similarity(z_student.unsqueeze(1), z_teacher.unsqueeze(0), dim=-1) / tau
            # Positive pairs: same index
            pos_sim = torch.diag(sim_matrix)
            # Denominator: sum over all pairs
            exp_sim = torch.exp(sim_matrix)
            denominator = exp_sim.sum(dim=1)
            loss = -torch.log(torch.exp(pos_sim) / denominator).mean()
            return loss

        loss_ckd_vs = contrastive_loss(outputs['z_vs'], outputs['z_teacher'])
        loss_ckd_vr = contrastive_loss(outputs['z_vr'], outputs['z_teacher'])
        loss_kd = loss_ckd_vs + loss_ckd_vr

        # Total Loss: L_Total = L_Task + λ_IB L_IB + λ_R L_R + λ_KD L_KD
        total_loss = loss_task + \
                     self.lambda_ib * loss_ib + \
                     self.lambda_r * loss_r + \
                     self.lambda_kd * loss_kd + \
                     self.lambda_orth * loss_orth

        return {
            'total_loss': total_loss,
            'loss_task': loss_task,
            'loss_r': loss_r,
            'loss_ib': loss_ib,
            'loss_ibt': loss_ibt,
            'loss_ibs': loss_ibs,
            'loss_near_opt': loss_near_opt,
            'loss_kd': loss_kd,
            'loss_ckd_vs': loss_ckd_vs,
            'loss_ckd_vr': loss_ckd_vr,
            'loss_orth': loss_orth
        }
