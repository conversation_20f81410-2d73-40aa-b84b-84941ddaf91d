#!/usr/bin/python
# -*- coding: UTF-8 -*-
from gae_datasets import get_dataset
from scipy import sparse
from scipy.sparse import csr_matrix
from scipy.sparse import save_npz
from scipy.sparse import load_npz
# from evaluate_embedding import evaluate_embedding
from scipy.sparse import lil_matrix

import numpy as np
A = np.array([[1,2,0],[0,0,3],[1,0,4]])
sA = sparse.csr_matrix(A)
# sparse.save_npz('./my_csr_adj.npz', sA)
# csr_matrix_variable = sparse.load_npz('csr_adj.npz')

def convert_csr_adj2_edgelist():

    return

def load_adj(y, dataset_name):
    csr_y_adj = load_npz("./data." + dataset_name + "/" + dataset_name + "_csr_y.npz")
    return csr_y_adj
def save_y2csr(tensor_y, dataset_name):
    y_ndarry = tensor_y.numpy()
    y_csr = csr_matrix(y_ndarry)
    path = "./data/" + dataset_name + "/" + dataset_name + "_csr_y.npz"
    save_npz(path, y_csr)
    return
def save_adj(edge_index_numpy, num_graphs, dataset_name):
    csr_adj = convert_numpy_csr_adj(edge_index_numpy, num_graphs)
    save_npz("./data/" + dataset_name + "/" + dataset_name + "_csr_adj.npz", csr_adj)
    return
def corect_save_adj(dataset, edge_index_numpy, num_nodes, dataset_name):
    def parse_node_toal_cnt(dataset):
        slice_cnt = dataset.slices['edge_index'].numpy()
        return slice_cnt

    slice_cnt = parse_node_toal_cnt(dataset)
    convert_numpy_csr_adj_4_many_graphs(edge_index_numpy, num_nodes, slice_cnt)
    return
# sA = sparse.load_npz('./csr_adj.npz')
def convert_numpy_csr_adj_4_many_graphs(edge_index_numpy, num_nodes, slice_cnt):
    base = 0
    cnt = 0
    for upper in slice_cnt:
        if upper != 0:
            lst_data = []
            lst_indptr = []
            lst_indices = []
            # cnt = np.nditer(slice_cnt)[i]
            tmp_lil_matrix = lil_matrix((num_nodes, num_nodes))
            for i in range(base, upper):
                tmp_adj_row = edge_index_numpy[0][i]
                tmp_adj_col = edge_index_numpy[1][i]
                tmp_lil_matrix[tmp_adj_row, tmp_adj_col] = 1
                tmp_csr_matrix = csr_matrix(tmp_lil_matrix)
                print("已经处理完第" + str(cnt) + "个图的第" + str(i) + "个节点，一共" + str(upper) + "个节点" + "一共" + str(len(slice_cnt) - 1) + "个图")
            print("./data/" + dataset_name + "/" + "all_graphs/" + dataset_name + "_" + str(cnt) + "_csr_adj.npz")
            save_npz("./data/" + dataset_name + "/" + "all_graphs/" + dataset_name + "_" + str(cnt) + "_csr_adj.npz",
                         tmp_csr_matrix)
            print("已经处理完第" + str(cnt) + "个图，一共" + str(len(slice_cnt) - 1) + "个")
            cnt = cnt + 1
        base = upper

    return
def convert_numpy_csr_adj(edge_index_numpy, num_graphs):
    [rows_edge_index, cols_edge_index] = edge_index_numpy.shape
    lst_data = []
    lst_indptr = []
    lst_indices = []

    tmp_np_matrix = np.zeros((num_graphs, num_graphs))
    for i in range(cols_edge_index):
        tmp_adj_row = edge_index_numpy[0][i]
        tmp_adj_col = edge_index_numpy[1][i]
        if  tmp_adj_row < num_graphs and tmp_adj_col < num_graphs:
            tmp_np_matrix[tmp_adj_row][tmp_adj_col] = 1
        print("已处理边第"+str(i) + " 个，一共 " + str(cols_edge_index))

    cnt_non_zero = 0
    lst_count_non_zero_each_row = []
    for i in range(num_graphs):
        non_zero_each_row = 0
        lst_indptr.append(cnt_non_zero)
        for j in range(num_graphs):
            if tmp_np_matrix[i][j] != 0:
                lst_data.append(1)
                cnt_non_zero = cnt_non_zero + 1
                non_zero_each_row = non_zero_each_row + 1
                lst_indices.append(j)
                print("已处理边第" + str(i) + " 行，第 " + str(j) + " 列")
        lst_count_non_zero_each_row.append(non_zero_each_row)

    # tmp_np_degree_matrix = np.zeros((num_nodes, num_nodes))

    # lst_indptr.append(cnt_non_zero)
    # indptr = np.array(lst_indptr)
    # indices = np.array(lst_indices)
    # data = np.array(lst_data)
    print("开始")
    # import datetime
    # starttime = datetime.datetime.now()

    new_csr_adj = csr_matrix(tmp_np_matrix)
    del tmp_np_matrix

    # new_csr_adj = csr_matrix((data, indices, indptr), shape=(num_nodes, num_nodes))
    # endtime = datetime.datetime.now()
    # print(endtime - starttime)
    print("结束")
    # csr_adj = csr_matrix((np.array(list_adj_row), np.array(list_adj_col), np.array(list_data)), shape=(num_nodes, num_nodes))
    return new_csr_adj
if __name__ == '__main__':
    # dataset_name = "NCI109"
    # dataset_name = "FRANKENSTEIN"
    # dataset_name = "Mutagenicity"
    # dataset_name = "deezer_ego_nets"
    # dataset_name = "DD"
    dataset_name = "IMDB-BINARY"

    sparse = True
    feat_str = "deg+odeg100"
    data_root = "data"
    dataset = get_dataset(
        dataset_name, sparse=True, feat_str=feat_str, root=data_root)
    graph_data = dataset.data
    edge_index = graph_data.edge_index
    x = graph_data.x
    y = graph_data.y
    num_graphs = graph_data.y.size(0)
    num_nodes = graph_data.num_nodes
    edge_index_numpy = edge_index.numpy()
    save_adj(edge_index_numpy, num_graphs, dataset_name)
    corect_save_adj(dataset, edge_index_numpy, num_nodes, dataset_name)
    csr_adj = load_npz("./data/" + dataset_name + "/" + dataset_name + "_csr_adj.npz")
    save_y2csr(y, dataset_name)

