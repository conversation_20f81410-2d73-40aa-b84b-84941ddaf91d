"""
CLUB互信息估计接口使用示例

这个文件展示了如何使用MutualInformationEstimator类来计算互信息。
实现了CLUB论文中的变分上界估计方法。

CLUB优化函数:
- 标准版本: Î_vCLUB = (1/N)Σ[log q_θ(y_i|x_i) - (1/N)Σ log q_θ(y_j|x_i)]
- 采样版本: Î_vCLUB-S = (1/N)Σ[log q_θ(y_i|x_i) - log q_θ(y_k'|x_i)]

作者: AI研究者
用途: 顶会论文实验
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from mi_estimators import MutualInformationEstimator

def generate_correlated_data(n_samples=1000, x_dim=10, y_dim=5, correlation=0.8):
    """
    生成具有已知相关性的合成数据用于测试
    
    Args:
        n_samples: 样本数量
        x_dim: X的维度
        y_dim: Y的维度  
        correlation: 相关性强度
    
    Returns:
        x_samples, y_samples: 生成的数据
    """
    # 生成相关的高斯数据
    mean = np.zeros(x_dim + y_dim)
    
    # 构造协方差矩阵
    cov = np.eye(x_dim + y_dim)
    # 在X和Y之间添加相关性
    for i in range(x_dim):
        for j in range(y_dim):
            cov[i, x_dim + j] = correlation
            cov[x_dim + j, i] = correlation
    
    # 生成数据
    data = np.random.multivariate_normal(mean, cov, n_samples)
    x_samples = torch.FloatTensor(data[:, :x_dim])
    y_samples = torch.FloatTensor(data[:, x_dim:])
    
    return x_samples, y_samples

def generate_categorical_data(n_samples=1000, x_dim=10, n_classes=5):
    """
    生成X为连续变量，Y为分类变量的数据
    """
    x_samples = torch.randn(n_samples, x_dim)
    
    # 让Y依赖于X的某些维度
    logits = torch.matmul(x_samples[:, :3], torch.randn(3, n_classes))
    y_samples = torch.multinomial(torch.softmax(logits, dim=1), 1).squeeze()
    
    return x_samples, y_samples

def example_continuous_mi_estimation():
    """
    示例1: 连续变量间的互信息估计
    """
    print("=" * 60)
    print("示例1: 连续变量互信息估计")
    print("=" * 60)
    
    # 生成测试数据
    x_samples, y_samples = generate_correlated_data(n_samples=2000, x_dim=8, y_dim=4, correlation=0.7)
    
    # 初始化MI估计器 - 使用标准CLUB
    mi_estimator = MutualInformationEstimator(
        x_dim=8, 
        y_dim=4, 
        hidden_size=256,
        estimator_type='club'
    )
    
    print(f"数据形状: X={x_samples.shape}, Y={y_samples.shape}")
    print("开始训练变分网络 q_θ(Y|X)...")
    
    # 训练变分网络
    mi_estimator.train_variational_network(
        x_samples, y_samples, 
        epochs=200, 
        lr=1e-3, 
        batch_size=128,
        verbose=True
    )
    
    # 估计互信息
    mi_value = mi_estimator.estimate_mi(x_samples, y_samples)
    print(f"\n估计的互信息值: {mi_value:.4f} nats")
    print(f"估计的互信息值: {mi_value/np.log(2):.4f} bits")
    
    # 计算对数似然
    log_likelihood = mi_estimator.compute_log_likelihood(x_samples, y_samples)
    print(f"平均对数似然: {log_likelihood:.4f}")
    
    return mi_value

def example_categorical_mi_estimation():
    """
    示例2: 连续-分类变量间的互信息估计
    """
    print("\n" + "=" * 60)
    print("示例2: 连续-分类变量互信息估计")
    print("=" * 60)
    
    # 生成测试数据
    x_samples, y_samples = generate_categorical_data(n_samples=2000, x_dim=10, n_classes=5)
    
    # 初始化MI估计器 - 使用分类CLUB
    mi_estimator = MutualInformationEstimator(
        x_dim=10,
        label_num=5,
        hidden_size=128,
        estimator_type='club_categorical'
    )
    
    print(f"数据形状: X={x_samples.shape}, Y={y_samples.shape}")
    print("开始训练变分网络 q_θ(Y|X)...")
    
    # 训练变分网络
    mi_estimator.train_variational_network(
        x_samples, y_samples,
        epochs=150,
        lr=1e-3,
        batch_size=128,
        verbose=True
    )
    
    # 估计互信息
    mi_value = mi_estimator.estimate_mi(x_samples, y_samples)
    print(f"\n估计的互信息值: {mi_value:.4f} nats")
    print(f"估计的互信息值: {mi_value/np.log(2):.4f} bits")
    
    return mi_value

def compare_club_variants():
    """
    示例3: 比较不同CLUB变体的性能
    """
    print("\n" + "=" * 60)
    print("示例3: 比较不同CLUB变体")
    print("=" * 60)
    
    # 生成测试数据
    x_samples, y_samples = generate_correlated_data(n_samples=1500, x_dim=6, y_dim=3, correlation=0.6)
    
    estimator_types = ['club', 'club_mean', 'club_sample']
    results = {}
    
    for est_type in estimator_types:
        print(f"\n训练 {est_type.upper()} 估计器...")
        
        mi_estimator = MutualInformationEstimator(
            x_dim=6,
            y_dim=3,
            hidden_size=128,
            estimator_type=est_type
        )
        
        # 训练
        mi_estimator.train_variational_network(
            x_samples, y_samples,
            epochs=100,
            lr=1e-3,
            verbose=False
        )
        
        # 估计MI
        mi_value = mi_estimator.estimate_mi(x_samples, y_samples)
        results[est_type] = mi_value
        
        print(f"{est_type.upper()} MI估计: {mi_value:.4f} nats ({mi_value/np.log(2):.4f} bits)")
    
    print(f"\n结果总结:")
    for est_type, mi_val in results.items():
        print(f"  {est_type.upper()}: {mi_val:.4f} nats")
    
    return results

def advanced_usage_example():
    """
    示例4: 高级用法 - 模型保存和加载
    """
    print("\n" + "=" * 60)
    print("示例4: 模型保存和加载")
    print("=" * 60)
    
    # 生成数据
    x_samples, y_samples = generate_correlated_data(n_samples=1000, x_dim=5, y_dim=3)
    
    # 训练模型
    mi_estimator = MutualInformationEstimator(x_dim=5, y_dim=3, hidden_size=64, estimator_type='club')
    mi_estimator.train_variational_network(x_samples, y_samples, epochs=50, verbose=False)
    
    # 保存模型
    model_path = "club_model.pth"
    mi_estimator.save_model(model_path)
    print(f"模型已保存到: {model_path}")
    
    # 加载模型
    new_estimator = MutualInformationEstimator(x_dim=5, y_dim=3, hidden_size=64, estimator_type='club')
    new_estimator.load_model(model_path)
    print("模型已加载")
    
    # 验证结果一致性
    mi_original = mi_estimator.estimate_mi(x_samples, y_samples)
    mi_loaded = new_estimator.estimate_mi(x_samples, y_samples)
    
    print(f"原始模型MI估计: {mi_original:.6f}")
    print(f"加载模型MI估计: {mi_loaded:.6f}")
    print(f"差异: {abs(mi_original - mi_loaded):.8f}")

if __name__ == "__main__":
    # 设置随机种子以确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)
    
    print("CLUB互信息估计接口演示")
    print("实现论文中的变分上界方法")
    
    # 运行所有示例
    example_continuous_mi_estimation()
    example_categorical_mi_estimation() 
    compare_club_variants()
    advanced_usage_example()
    
    print("\n" + "=" * 60)
    print("所有示例运行完成!")
    print("=" * 60)
