from scipy import sparse
from scipy.sparse import csr_matrix
from scipy.sparse import save_npz
from scipy.sparse import load_npz

# dataset_name = "NCI109"
# dataset_name = "FRANKENSTEIN"
dataset_name = "IMDB-BINARY"
# dataset_name = "DD"
# dataset_name = "Mutagenicity"
# dataset_name = "deezer_ego_nets"

csr_matrix_variable = sparse.load_npz('./data/' + dataset_name + '/' + dataset_name + '_csr_adj.npz')
matrix_variable = csr_matrix_variable.toarray()
# matrix_dense = csr_matrix_variable.todense()
# print(matrix_variable)
[m, n] = matrix_variable.shape
all_line = ""
pth = './data/' + dataset_name + '.edgelist'
with open('./data/' + dataset_name + '/'+ dataset_name + '.edgelist', 'ab') as f:
    f.seek(0)
    f.truncate()
    for i in range(m):
        line = ""
        for j in range(n):
            if matrix_variable[i][j] != 0:
                line = line + (str(i) + " " +str(j) + "\n")
            # if(j == n-1):
            #     line = line.strip()
        f.write(line.encode('utf-8'))
        print("已经处理完第" + str(i) + "个节点，一共" + str(m) + "个节点")

