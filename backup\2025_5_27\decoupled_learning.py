import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import Linear, BatchNorm1d
from res_gcn import ResGCN
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class GraphMLMutiView(torch.nn.Module):
    def __init__(self, dataset, hidden, layer_num_heads, num_view, num_feat_layers=1, num_conv_layers=3, num_fc_layers=2, residual=False, res_branch="BNConvReLU", global_pool="sum", dropout=0,edge_norm=True):

        super(GraphMLMutiView, self).__init__()
        self.gat_layers = nn.ModuleList()
        for i in range(num_view):
            self.gat_layers.append(ResGCN(dataset, hidden, num_feat_layers, num_conv_layers, num_fc_layers, gfn=False, collapse=False,
                          residual=residual, res_branch=res_branch,
                          global_pool=global_pool, dropout=dropout,
                          edge_norm=edge_norm))
        # 使用解耦学习机制替代注意力机制
        self.decoupled_learning = DecoupledLearning(num_view=num_view, feature_dim=hidden)
        
    def forward(self, dataset_lst):
        semantic_embeddings = []
        for i, data_i in enumerate(dataset_lst):
            semantic_embeddings.append(self.gat_layers[i].forward_cl(data_i))
        semantic_embeddings = torch.stack(semantic_embeddings, dim=1)  # (N, M, D * K)
        return self.decoupled_learning(semantic_embeddings)  # (N, D * K)

    def loss_cl(self, x1, x2):
        T = 0.5
        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        # tmp_dim = torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.exp(sim_matrix / T)
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        loss = pos_sim / (sim_matrix.sum(dim=1) - pos_sim)
        loss = - torch.log(loss).mean()
        return loss

    def loss_cross_dm_ml(self, x1, x2, pos_weights, epoch, index_data_traverse, \
                pretrain_check_size, data, carlibrator_switch):
        T = 0.5

        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        # tmp_dim = torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = sim_matrix / T
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        # loss = torch.exp(confidence * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        if epoch == 1 and index_data_traverse == 0:
            loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
            # loss_mark = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        else:
            label_arr = data.y[:pretrain_check_size]
            batch_weight = np.array([1.0 for i in range(batch_size)])
            dim_check_size = list(label_arr.shape)[0]
            for i in range(dim_check_size):
                label_index = label_arr[i]
                batch_weight[i] = pos_weights[label_index][label_index]
            batch_weight = torch.from_numpy(batch_weight).to(device)
            # tmp = batch_weight * pos_sim
            if carlibrator_switch:
                loss = torch.exp(batch_weight * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)
            else:
                loss = torch.exp(batch_weight) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)

        loss = - torch.log(loss + 1).mean()
        # loss2 = - torch.log(loss_mark + 1).mean()

        return loss

    def loss_ml(self, x1, x2, pos_weights, epoch, index_data_traverse, \
                pretrain_check_size, data, carlibrator_switch):
        T = 0.5

        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        tmp_dim = torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = sim_matrix / T
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        # loss = torch.exp(confidence * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        if epoch == 1 and index_data_traverse == 0:
            loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
            loss_mark = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        else:
            label_arr = data.y[:pretrain_check_size]
            batch_weight = np.array([1.0 for i in range(batch_size)])
            dim_check_size = list(label_arr.shape)[0]
            for i in range(dim_check_size):
                label_index = label_arr[i]
                batch_weight[i] = pos_weights[label_index][label_index]
            batch_weight = torch.from_numpy(batch_weight).to(device)
            # tmp = batch_weight * pos_sim
            if carlibrator_switch:
                loss = torch.exp(batch_weight * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)
            else:
                loss = torch.exp(batch_weight) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)

            # loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
            # loss_mark = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        # loss = - torch.log(loss + 1.0).mean()
        # tmp_loss_mark = loss_mark + 1

        loss = - torch.log(loss + 1).mean()
        # loss2 = - torch.log(loss_mark + 1).mean()

        return loss


class DecoupledLearning(torch.nn.Module):
    """
    解耦学习机制，类似于自编码器，包含编码器和解码器部分
    用于替代原有的注意力机制
    """
    def __init__(self, num_view, feature_dim, hidden_dim=128, latent_dim=64):
        super(DecoupledLearning, self).__init__()
        self.num_view = num_view
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim
        self.latent_dim = latent_dim
        
        # 编码器部分 - 将多视图嵌入编码为潜在表示
        self.encoder = nn.Sequential(
            nn.Linear(num_view * feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, latent_dim)
        )
        
        # 解码器部分 - 将潜在表示解码回原始视图
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, num_view * feature_dim)
        )
        
        # 用于融合多视图表示的投影层
        self.projection = nn.Linear(latent_dim, feature_dim)
        
    def forward(self, z):
        """
        参数:
            z: 多视图嵌入，形状为 (N, M, D)，其中 N 是样本数，M 是视图数，D 是特征维度
        返回:
            融合后的表示，形状为 (N, D)
        """
        batch_size, num_views, feat_dim = z.shape
        
        # 将多视图嵌入展平
        z_flat = z.view(batch_size, -1)  # (N, M*D)
        
        # 编码
        latent = self.encoder(z_flat)  # (N, latent_dim)
        
        # 解码（用于重建损失）
        reconstructed = self.decoder(latent)  # (N, M*D)
        
        # 计算重建损失（在训练时使用）
        self.reconstruction_loss = F.mse_loss(reconstructed, z_flat)
        
        # 投影到最终的表示空间
        output = self.projection(latent)  # (N, D)
        
        return output
    
    def get_reconstruction_loss(self):
        """
        返回最近一次前向传播的重建损失
        """
        return self.reconstruction_loss