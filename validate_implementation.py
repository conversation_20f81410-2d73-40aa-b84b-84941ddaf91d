#!/usr/bin/env python3
"""
Validation script for the Disentangled Information Bottleneck Knowledge Distillation GNN framework.
This script performs static analysis and validation of the implementation.
"""

import ast
import inspect
import sys
import os

def validate_models_py():
    """Validate models.py implementation"""
    print("Validating models.py...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the AST
        tree = ast.parse(content)
        
        # Check for required classes
        classes = [node.name for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
        required_classes = ['HSIC', 'Student', 'Classifier', 'DisentangledIBKD_GNN']
        
        for cls in required_classes:
            if cls in classes:
                print(f"  ✓ Found class: {cls}")
            else:
                print(f"  ❌ Missing class: {cls}")
                return False
        
        # Check for required methods in DisentangledIBKD_GNN
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == 'DisentangledIBKD_GNN':
                methods = [n.name for n in node.body if isinstance(n, ast.FunctionDef)]
                required_methods = ['__init__', 'forward', 'loss_function']
                
                for method in required_methods:
                    if method in methods:
                        print(f"  ✓ Found method: DisentangledIBKD_GNN.{method}")
                    else:
                        print(f"  ❌ Missing method: DisentangledIBKD_GNN.{method}")
                        return False
        
        print("  ✓ models.py validation passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Error validating models.py: {e}")
        return False

def validate_attention_py():
    """Validate attention.py implementation"""
    print("\nValidating attention.py...")
    
    try:
        with open('attention.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the AST
        tree = ast.parse(content)
        
        # Check for required classes
        classes = [node.name for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
        required_classes = ['DecoupledAttention', 'GraphMLMutiView']
        
        for cls in required_classes:
            if cls in classes:
                print(f"  ✓ Found class: {cls}")
            else:
                print(f"  ❌ Missing class: {cls}")
                return False
        
        # Check for fusion weights in DecoupledAttention
        if 'fusion_weights' in content:
            print("  ✓ Found fusion_weights implementation")
        else:
            print("  ❌ Missing fusion_weights implementation")
            return False
        
        # Check for multi-view encoding formula
        if 'Θ_Φ' in content or 'Z_φ' in content:
            print("  ✓ Found multi-view encoding formula implementation")
        else:
            print("  ⚠️  Multi-view encoding formula comments may be missing")
        
        print("  ✓ attention.py validation passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Error validating attention.py: {e}")
        return False

def validate_loss_functions():
    """Validate loss function implementation"""
    print("\nValidating loss functions...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required loss components
        required_losses = [
            'L_IBT',  # Teacher IB loss
            'L_IBS',  # Student IB loss  
            'L_Near_OPT',  # Near optimal loss
            'L_CKD',  # Contrastive knowledge distillation
            'hsic',   # HSIC orthogonality
            'total_loss'  # Total loss
        ]
        
        for loss in required_losses:
            if loss in content:
                print(f"  ✓ Found loss component: {loss}")
            else:
                print(f"  ❌ Missing loss component: {loss}")
                return False
        
        # Check for theoretical parameters
        theoretical_params = ['beta_t', 'beta_s', 'gamma_s', 'kappa', 'R']
        for param in theoretical_params:
            if param in content:
                print(f"  ✓ Found theoretical parameter: {param}")
            else:
                print(f"  ❌ Missing theoretical parameter: {param}")
                return False
        
        print("  ✓ Loss functions validation passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Error validating loss functions: {e}")
        return False

def validate_training_integration():
    """Validate training integration"""
    print("\nValidating training integration...")
    
    try:
        # Check train_eval.py
        with open('train_eval.py', 'r', encoding='utf-8') as f:
            train_content = f.read()
        
        # Check DMG_pretrain_main.py
        with open('DMG_pretrain_main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        # Check for parameter passing
        if 'beta_t' in train_content and 'beta_s' in train_content:
            print("  ✓ Found theoretical parameters in training")
        else:
            print("  ❌ Missing theoretical parameters in training")
            return False
        
        # Check for new arguments in main
        new_args = ['beta_t', 'beta_s', 'gamma_s', 'kappa', 'redundancy_upper_bound']
        for arg in new_args:
            if arg in main_content:
                print(f"  ✓ Found argument: {arg}")
            else:
                print(f"  ❌ Missing argument: {arg}")
                return False
        
        print("  ✓ Training integration validation passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Error validating training integration: {e}")
        return False

def validate_theoretical_framework():
    """Validate theoretical framework implementation"""
    print("\nValidating theoretical framework...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for key theoretical concepts
        concepts = {
            'Information Bottleneck': ['kl_divergence', 'I(y', 'I(z'],
            'Knowledge Distillation': ['contrastive', 'teacher', 'student'],
            'Multi-view Learning': ['fusion_weights', 'multi-view', 'view'],
            'Disentangled Learning': ['z_vs', 'z_vr', 'redundant', 'non-redundant'],
            'HSIC Orthogonality': ['HSIC', 'orthogonal', 'independence']
        }
        
        for concept, keywords in concepts.items():
            found = any(keyword in content.lower() for keyword in keywords)
            if found:
                print(f"  ✓ Found {concept} implementation")
            else:
                print(f"  ❌ Missing {concept} implementation")
                return False
        
        print("  ✓ Theoretical framework validation passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Error validating theoretical framework: {e}")
        return False

def main():
    """Run all validations"""
    print("=" * 70)
    print("Validating Disentangled IBKD GNN Framework Implementation")
    print("=" * 70)
    
    validations = [
        validate_models_py,
        validate_attention_py,
        validate_loss_functions,
        validate_training_integration,
        validate_theoretical_framework
    ]
    
    all_passed = True
    for validation in validations:
        if not validation():
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 All validations passed! Implementation appears to be complete.")
        print("\nNext steps:")
        print("1. Test with actual data")
        print("2. Tune hyperparameters (beta_t, beta_s, gamma_s, kappa, R)")
        print("3. Run cross-domain experiments")
        print("4. Compare with baseline methods")
    else:
        print("❌ Some validations failed. Please review the implementation.")
    print("=" * 70)
    
    return all_passed

if __name__ == "__main__":
    main()
