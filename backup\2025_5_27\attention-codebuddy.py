import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import Linear, BatchNorm1d
from res_gcn import ResGCN
from decoupled_learning import DecoupledLearning
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# class GraphMLModel(torch.nn.Module):
#     def __init__(self,num_view, num_head, in_size, hidden_size):
#         super(GraphMLModel, self).__init__()
#     def forward(self, z):
#         print("123")

class GraphMLMutiView(torch.nn.Module):
    # def __init__(self, dataset, resgcn, hidden, layer_num_heads, num_view=4,num_feat_layers=1, num_conv_layers=3, num_fc_layers=2, residual=False, res_branch="BNConvReLU", global_pool="sum", dropout=0,edge_norm=True):
    def __init__(self, dataset, hidden, layer_num_heads, num_view, num_feat_layers=1, num_conv_layers=3, num_fc_layers=2, residual=False, res_branch="BNConvReLU", global_pool="sum", dropout=0,edge_norm=True):

        super(GraphMLMutiView, self).__init__()
        self.gat_layers = nn.ModuleList()
        for i in range(num_view):
            self.gat_layers.append(ResGCN(dataset, hidden, num_feat_layers, num_conv_layers, num_fc_layers, gfn=False, collapse=False,
                          residual=residual, res_branch=res_branch,
                          global_pool=global_pool, dropout=dropout,
        self.semantic_attention = Attention(num_view=num_view)  # Changed to DecoupledLearning
    def forward(self, dataset_lst):
        semantic_embeddings = []
        for i, data_i in enumerate(dataset_lst):
            semantic_embeddings.append(self.gat_layers[i].forward_cl(data_i))
        semantic_embeddings = torch.stack(semantic_embeddings, dim=1)  # (N, M, D * K)
        return self.semantic_attention(semantic_embeddings)  # (N, D * K)

    def loss_cl(self, x1, x2):
        T = 0.5
        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        # tmp_dim = torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.exp(sim_matrix / T)
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        loss = pos_sim / (sim_matrix.sum(dim=1) - pos_sim)
        loss = - torch.log(loss).mean()
        return loss

    def loss_cross_dm_ml(self, x1, x2, pos_weights, epoch, index_data_traverse, \
                pretrain_check_size, data, carlibrator_switch):
        T = 0.5

        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        # tmp_dim = torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = sim_matrix / T
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        # loss = torch.exp(confidence * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        if epoch == 1 and index_data_traverse == 0:
            loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
            # loss_mark = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        else:
            label_arr = data.y[:pretrain_check_size]
            batch_weight = np.array([1.0 for i in range(batch_size)])
            dim_check_size = list(label_arr.shape)[0]
            for i in range(dim_check_size):
                label_index = label_arr[i]
                batch_weight[i] = pos_weights[label_index][label_index]
            batch_weight = torch.from_numpy(batch_weight).to(device)
            # tmp = batch_weight * pos_sim
            if carlibrator_switch:
                loss = torch.exp(batch_weight * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)
            else:
                loss = torch.exp(batch_weight) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)



        loss = - torch.log(loss + 1).mean()
        # loss2 = - torch.log(loss_mark + 1).mean()

        return loss

    def loss_ml(self, x1, x2, pos_weights, epoch, index_data_traverse, \
                pretrain_check_size, data, carlibrator_switch):
        T = 0.5

        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        tmp_dim = torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = sim_matrix / T
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        # loss = torch.exp(confidence * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        if epoch == 1 and index_data_traverse == 0:
            loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
            loss_mark = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        else:
            label_arr = data.y[:pretrain_check_size]
            batch_weight = np.array([1.0 for i in range(batch_size)])
            dim_check_size = list(label_arr.shape)[0]
            for i in range(dim_check_size):
                label_index = label_arr[i]
                batch_weight[i] = pos_weights[label_index][label_index]
            batch_weight = torch.from_numpy(batch_weight).to(device)
            # tmp = batch_weight * pos_sim
            if carlibrator_switch:
                loss = torch.exp(batch_weight * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)
            else:
                loss = torch.exp(batch_weight) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)

            # loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
            # loss_mark = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        # loss = - torch.log(loss + 1.0).mean()
        # tmp_loss_mark = loss_mark + 1

        loss = - torch.log(loss + 1).mean()
        # loss2 = - torch.log(loss_mark + 1).mean()

        return loss



class Attention(torch.nn.Module):
    def __init__(self, in_size, hidden_size=128):
        super(Attention, self).__init__()
        self.project = nn.Sequential(
            nn.Linear(in_size, hidden_size),
            nn.Tanh(),
            nn.Linear(hidden_size, 1, bias=False)
        )
    def forward(self, z):
        # z: (N , M , D * K)
        w = self.project(z).mean(0)                    # (M, 1)
        beta = torch.softmax(w, dim=0)                 # (M, 1)
        beta = beta.expand((z.shape[0],) + beta.shape) # (N, M, 1)
        return (beta * z).sum(1)                       # (N, D * K)`
