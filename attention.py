import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import Linear, BatchNorm1d
from res_gcn import ResGCN
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class Encoder(nn.Module):
    def __init__(self, in_features, latent_dim):
        super(Encoder, self).__init__()
        self.fc1 = nn.Linear(in_features, latent_dim * 2)
        self.relu = nn.ReLU()
        self.fc_mu = nn.Linear(latent_dim * 2, latent_dim)
        self.fc_logvar = nn.Linear(latent_dim * 2, latent_dim)

    def forward(self, x):
        h = self.relu(self.fc1(x))
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar

class Decoder(nn.Module):
    def __init__(self, latent_dim, out_features):
        super(Decoder, self).__init__()
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, latent_dim * 2), 
            nn.ReLU(),
            nn.Linear(latent_dim * 2, out_features)
        )

    def forward(self, x):
        return self.decoder(x)

class DecoupledAttention(nn.Module):
    def __init__(self, num_view, in_features, latent_dim, hidden_size=128):
        super(DecoupledAttention, self).__init__()
        self.num_view = num_view
        self.in_features = in_features
        self.latent_dim = latent_dim

        self.encoders = nn.ModuleList([Encoder(in_features, latent_dim) for _ in range(num_view)])
        self.decoders = nn.ModuleList([Decoder(latent_dim, in_features) for _ in range(num_view)])

        self.project_latent = nn.Sequential(
            nn.Linear(latent_dim, hidden_size),
            nn.Tanh(),
            nn.Linear(hidden_size, 1, bias=False)
        )

    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def forward(self, z):
        N, M, D_K = z.shape
        assert M == self.num_view
        assert D_K == self.in_features

        all_mu = []
        all_logvar = []
        all_latent_samples = []
        reconstructed_views = []

        for i in range(self.num_view):
            view_data = z[:, i, :]
            mu_i, logvar_i = self.encoders[i](view_data)
            z_i = self.reparameterize(mu_i, logvar_i)
            all_mu.append(mu_i)
            all_logvar.append(logvar_i)
            all_latent_samples.append(z_i)

            reconstructed_i = self.decoders[i](z_i)
            reconstructed_views.append(reconstructed_i)

        mu_stack = torch.stack(all_mu, dim=1)
        logvar_stack = torch.stack(all_logvar, dim=1)
        latent_samples_stack = torch.stack(all_latent_samples, dim=1)

        w_latent = self.project_latent(latent_samples_stack)
        beta_latent = torch.softmax(w_latent, dim=1)

        # Fuse the distributions' parameters, not the samples
        fused_mu = (beta_latent * mu_stack).sum(1)
        fused_logvar = (beta_latent * logvar_stack).sum(1) # This is a simplification. A better fusion would be more complex.

        reconstructed_stack = torch.stack(reconstructed_views, dim=1)

        return fused_mu, fused_logvar, reconstructed_stack, (mu_stack, logvar_stack) 

class GraphMLMutiView(torch.nn.Module):
    def __init__(self, dataset, hidden, layer_num_heads, num_view, latent_dim_decoupled, num_feat_layers=1, num_conv_layers=3, num_fc_layers=2, residual=False, res_branch="BNConvReLU", global_pool="sum", dropout=0,edge_norm=True):

        super(GraphMLMutiView, self).__init__()
        self.gat_layers = nn.ModuleList()
        self.res_gcn_out_dim = hidden

        for i in range(num_view):
            # Important: Each view should have its own GCN instance
            gcn = ResGCN(dataset, hidden, num_feat_layers, num_conv_layers, num_fc_layers, gfn=False, collapse=False,
                          residual=residual, res_branch=res_branch,
                          global_pool=global_pool, dropout=dropout, edge_norm=edge_norm)
            self.gat_layers.append(gcn)

        self.semantic_attention = DecoupledAttention(num_view=num_view,
                                                      in_features=self.res_gcn_out_dim,
                                                      latent_dim=latent_dim_decoupled)

    def forward(self, dataset_lst):
        semantic_embeddings = []
        for i, data_i in enumerate(dataset_lst):
            # Using forward_gae to get node embeddings before pooling
            # NOTE: The original code used forward_cl, which doesn't exist in net_gae.py. Assuming forward_gae or forward_ml is intended.
            node_embeddings = self.gat_layers[i].forward_ml(data_i)
            # We need graph-level embeddings, so we apply global pooling here.
            graph_embedding = self.gat_layers[i].global_pool(node_embeddings, data_i.batch)
            semantic_embeddings.append(graph_embedding)

        semantic_embeddings_stack = torch.stack(semantic_embeddings, dim=1)

        fused_mu, fused_logvar, reconstructed_views, individual_dist_params = self.semantic_attention(semantic_embeddings_stack)

        # Return all necessary components for loss calculation
        return fused_mu, fused_logvar, reconstructed_views, individual_dist_params, semantic_embeddings_stack 

    def loss_cl(self, x1, x2):
        T = 0.5
        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = torch.exp(sim_matrix / T)
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        loss = pos_sim / (sim_matrix.sum(dim=1) - pos_sim)
        loss = - torch.log(loss).mean()
        return loss

    def loss_cross_dm_ml(self, x1, x2, pos_weights, epoch, index_data_traverse, \
                pretrain_check_size, data, carlibrator_switch):
        T = 0.5

        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = sim_matrix / T
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        if epoch == 1 and index_data_traverse == 0:
            loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        else:
            label_arr = data.y[:pretrain_check_size]
            batch_weight = np.array([1.0 for i in range(batch_size)])
            dim_check_size = list(label_arr.shape)[0]
            for i in range(dim_check_size):
                label_index = label_arr[i]
                batch_weight[i] = pos_weights[label_index][label_index]
            batch_weight = torch.from_numpy(batch_weight).to(device)
            if carlibrator_switch:
                loss = torch.exp(batch_weight * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)
            else:
                loss = torch.exp(batch_weight) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)

        loss = - torch.log(loss + 1).mean()
        return loss

    def loss_ml(self, x1, x2, pos_weights, epoch, index_data_traverse, \
                pretrain_check_size, data, carlibrator_switch):
        T = 0.5

        batch_size, _ = x1.size()
        x1_abs = x1.norm(dim=1)
        x2_abs = x2.norm(dim=1)
        sim_matrix = torch.einsum('ik,jk->ij', x1, x2) / torch.einsum('i,j->ij', x1_abs, x2_abs)
        sim_matrix = sim_matrix / T
        pos_sim = sim_matrix[range(batch_size), range(batch_size)]
        if epoch == 1 and index_data_traverse == 0:
            loss = torch.exp(pos_sim) / torch.exp(sim_matrix.sum(dim=1) - pos_sim)
        else:
            label_arr = data.y[:pretrain_check_size]
            batch_weight = np.array([1.0 for i in range(batch_size)])
            dim_check_size = list(label_arr.shape)[0]
            for i in range(dim_check_size):
                label_index = label_arr[i]
                batch_weight[i] = pos_weights[label_index][label_index]
            batch_weight = torch.from_numpy(batch_weight).to(device)
            if carlibrator_switch:
                loss = torch.exp(batch_weight * pos_sim) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)
            else:
                loss = torch.exp(batch_weight) / torch.exp(sim_matrix.sum(dim=1) - batch_weight * pos_sim)

        loss = - torch.log(loss + 1).mean()
        return loss
