#!/usr/bin/env python3
"""
测试简化的解耦架构 - 去掉注意力机制，直接通过解耦学习产生教师编码 Z_φ
"""

import ast
import sys

def test_decoupled_fusion_class():
    """测试 DecoupledFusion 类是否正确实现"""
    print("测试 DecoupledFusion 类...")
    
    try:
        with open('attention.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有 DecoupledFusion 类
        if 'class DecoupledFusion' in content:
            print("  ✓ 找到 DecoupledFusion 类")
        else:
            print("  ❌ 未找到 DecoupledFusion 类")
            return False
        
        # 检查是否去掉了注意力机制
        if 'project_latent' not in content or 'Tanh()' not in content:
            print("  ✓ 成功去掉了注意力机制")
        else:
            print("  ⚠️  可能仍包含注意力机制")
        
        # 检查是否返回教师编码
        if 'teacher_encoding' in content:
            print("  ✓ 正确返回教师编码 Z_φ")
        else:
            print("  ❌ 未找到教师编码返回")
            return False
        
        # 检查融合权重
        if 'fusion_weights' in content:
            print("  ✓ 保留了可学习的融合权重 Θ_Φ")
        else:
            print("  ❌ 缺少融合权重")
            return False
        
        print("  ✓ DecoupledFusion 类测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 测试 DecoupledFusion 时出错: {e}")
        return False

def test_graphml_multiview_integration():
    """测试 GraphMLMutiView 是否正确集成新的解耦机制"""
    print("\n测试 GraphMLMutiView 集成...")
    
    try:
        with open('attention.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用 DecoupledFusion
        if 'self.decoupled_fusion = DecoupledFusion' in content:
            print("  ✓ 正确使用 DecoupledFusion")
        else:
            print("  ❌ 未正确集成 DecoupledFusion")
            return False
        
        # 检查是否去掉了 semantic_attention
        if 'semantic_attention' not in content or 'DecoupledAttention' not in content:
            print("  ✓ 成功去掉了旧的注意力机制")
        else:
            print("  ❌ 仍然包含旧的注意力机制")
            return False
        
        # 检查 forward 方法是否返回教师编码
        if 'teacher_encoding,' in content and 'teacher_encoding_mu,' in content:
            print("  ✓ forward 方法正确返回教师编码")
        else:
            print("  ❌ forward 方法未正确返回教师编码")
            return False
        
        print("  ✓ GraphMLMutiView 集成测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 测试 GraphMLMutiView 集成时出错: {e}")
        return False

def test_disentangled_ibkd_gnn_adaptation():
    """测试 DisentangledIBKD_GNN 是否正确适配新架构"""
    print("\n测试 DisentangledIBKD_GNN 适配...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否正确接收教师编码
        if 'teacher_encoding,' in content and 'teacher_encoding_mu,' in content:
            print("  ✓ 正确接收教师编码")
        else:
            print("  ❌ 未正确接收教师编码")
            return False
        
        # 检查是否使用教师编码进行学生网络训练
        if 'self.student_vs(teacher_encoding)' in content:
            print("  ✓ 正确使用教师编码训练学生网络")
        else:
            print("  ❌ 未正确使用教师编码")
            return False
        
        # 检查是否保留了分类功能
        if 'self.classifier(z_vs)' in content:
            print("  ✓ 保留了下游分类功能")
        else:
            print("  ❌ 缺少分类功能")
            return False
        
        print("  ✓ DisentangledIBKD_GNN 适配测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 测试 DisentangledIBKD_GNN 适配时出错: {e}")
        return False

def test_architecture_simplification():
    """测试架构简化效果"""
    print("\n测试架构简化效果...")
    
    try:
        with open('attention.py', 'r', encoding='utf-8') as f:
            attention_content = f.read()
        
        with open('models.py', 'r', encoding='utf-8') as f:
            models_content = f.read()
        
        # 统计代码复杂度
        attention_lines = len(attention_content.split('\n'))
        models_lines = len(models_content.split('\n'))
        
        print(f"  📊 attention.py 行数: {attention_lines}")
        print(f"  📊 models.py 行数: {models_lines}")
        
        # 检查关键概念
        key_concepts = {
            '解耦学习': ['decoupled', 'fusion', 'Θ_Φ'],
            '教师编码': ['teacher_encoding', 'Z_φ'],
            '学生网络': ['student_vs', 'student_vr', 'z_vs', 'z_vr'],
            '信息瓶颈': ['information', 'bottleneck', 'kl_divergence'],
            '知识蒸馏': ['distillation', 'contrastive', 'L_CKD']
        }
        
        for concept, keywords in key_concepts.items():
            found = any(keyword in attention_content.lower() or keyword in models_content.lower() 
                       for keyword in keywords)
            if found:
                print(f"  ✓ 保留了{concept}核心功能")
            else:
                print(f"  ⚠️  {concept}功能可能缺失")
        
        # 检查是否去掉了冗余的注意力机制
        redundant_attention = ['project_latent', 'attention_weights', 'softmax']
        has_redundant = any(keyword in attention_content.lower() for keyword in redundant_attention)
        
        if not has_redundant:
            print("  ✓ 成功去掉了冗余的注意力机制")
        else:
            print("  ⚠️  可能仍包含冗余的注意力机制")
        
        print("  ✓ 架构简化测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 测试架构简化时出错: {e}")
        return False

def test_theoretical_framework_integrity():
    """测试理论框架完整性"""
    print("\n测试理论框架完整性...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查核心数学公式
        formulas = {
            'Z_φ = Θ_Φ X_Φ': ['Z_φ', 'Θ_Φ', 'fusion_weights'],
            'L_IBT': ['L_IBT', 'beta_t', 'kl_teacher'],
            'L_IBS': ['L_IBS', 'L_Near_OPT', 'beta_s'],
            'HSIC正交性': ['HSIC', 'z_vs', 'z_vr', 'orthogonal'],
            '对比学习': ['contrastive', 'L_CKD', 'InfoNCE']
        }
        
        for formula, keywords in formulas.items():
            found = any(keyword in content for keyword in keywords)
            if found:
                print(f"  ✓ 保留了{formula}实现")
            else:
                print(f"  ❌ 缺少{formula}实现")
                return False
        
        print("  ✓ 理论框架完整性测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 测试理论框架完整性时出错: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 70)
    print("测试简化的解耦架构 - 去掉注意力机制")
    print("=" * 70)
    
    tests = [
        test_decoupled_fusion_class,
        test_graphml_multiview_integration,
        test_disentangled_ibkd_gnn_adaptation,
        test_architecture_simplification,
        test_theoretical_framework_integrity
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 所有测试通过！简化架构实现成功。")
        print("\n核心改进:")
        print("1. ✅ 去掉了冗余的注意力机制")
        print("2. ✅ 直接通过解耦学习产生教师编码 Z_φ")
        print("3. ✅ 简化了计算流程，提高了效率")
        print("4. ✅ 保留了所有理论框架核心功能")
        print("5. ✅ 教师编码直接用于下游分类任务")
        print("\n架构流程:")
        print("多视图图 → 视图特定GCN → 解耦融合 → 教师编码Z_φ → 学生网络(z_vs,z_vr) → 分类")
    else:
        print("❌ 部分测试失败，请检查实现。")
    print("=" * 70)
    
    return all_passed

if __name__ == "__main__":
    main()
