#!/usr/bin/env python3
"""
Test script for the Disentangled Information Bottleneck Knowledge Distillation GNN framework.
This script validates that the theoretical framework implementation works correctly.
"""

import torch
import torch.nn as nn
import numpy as np
from models import DisentangledIBKD_GNN, Student, HSIC, Classifier
from attention import GraphMLMutiView, DecoupledAttention

def test_hsic():
    """Test HSIC implementation"""
    print("Testing HSIC implementation...")
    
    hsic = HSIC(sigma=1.0)
    batch_size = 32
    latent_dim = 64
    
    # Create random features
    z_vs = torch.randn(batch_size, latent_dim)  # Non-redundant features
    z_vr = torch.randn(batch_size, latent_dim)  # Redundant features
    
    # Test HSIC calculation
    hsic_value = hsic(z_vs, z_vr)
    print(f"HSIC value: {hsic_value.item():.4f}")
    
    # Test with identical features (should give high HSIC)
    hsic_identical = hsic(z_vs, z_vs)
    print(f"HSIC with identical features: {hsic_identical.item():.4f}")
    
    assert hsic_value.requires_grad, "HSIC should be differentiable"
    print("✓ HSIC test passed")

def test_student_networks():
    """Test Student network implementation"""
    print("\nTesting Student networks...")
    
    input_dim = 128
    output_dim = 64
    batch_size = 16
    
    # Create student networks
    student_vs = Student(input_dim, output_dim, student_type='vs')
    student_vr = Student(input_dim, output_dim, student_type='vr')
    
    # Test forward pass
    z_teacher = torch.randn(batch_size, input_dim)
    
    mu_vs, logvar_vs = student_vs(z_teacher)
    mu_vr, logvar_vr = student_vr(z_teacher)
    
    print(f"Student VS output shape: mu={mu_vs.shape}, logvar={logvar_vs.shape}")
    print(f"Student VR output shape: mu={mu_vr.shape}, logvar={logvar_vr.shape}")
    
    assert mu_vs.shape == (batch_size, output_dim)
    assert logvar_vs.shape == (batch_size, output_dim)
    assert mu_vr.shape == (batch_size, output_dim)
    assert logvar_vr.shape == (batch_size, output_dim)
    
    print("✓ Student networks test passed")

def test_decoupled_attention():
    """Test DecoupledAttention implementation"""
    print("\nTesting DecoupledAttention...")
    
    num_view = 4
    in_features = 128
    latent_dim = 64
    batch_size = 8
    
    attention = DecoupledAttention(num_view, in_features, latent_dim)
    
    # Create multi-view input
    z = torch.randn(batch_size, num_view, in_features)
    
    fused_mu, fused_logvar, reconstructed_views, individual_params = attention(z)
    
    print(f"Fused representation: mu={fused_mu.shape}, logvar={fused_logvar.shape}")
    print(f"Reconstructed views shape: {reconstructed_views.shape}")
    
    assert fused_mu.shape == (batch_size, latent_dim)
    assert fused_logvar.shape == (batch_size, latent_dim)
    assert reconstructed_views.shape == (batch_size, num_view, in_features)
    
    print("✓ DecoupledAttention test passed")

def test_loss_computation():
    """Test loss function computation"""
    print("\nTesting loss computation...")
    
    # Create mock teacher model
    class MockTeacher(nn.Module):
        def forward(self, x):
            batch_size = len(x[0].batch.unique()) if hasattr(x[0], 'batch') else x[0].size(0)
            latent_dim = 64
            return (torch.randn(batch_size, latent_dim), 
                   torch.randn(batch_size, latent_dim),
                   torch.randn(batch_size, 4, 128),
                   (torch.randn(batch_size, 4, latent_dim), torch.randn(batch_size, 4, latent_dim)),
                   torch.randn(batch_size, 4, 128))
    
    teacher = MockTeacher()
    latent_dim = 64
    num_classes = 10
    
    # Create DisentangledIBKD_GNN model
    model = DisentangledIBKD_GNN(
        teacher_model=teacher,
        latent_dim=latent_dim,
        num_classes=num_classes,
        lambda_ib=1.0,
        lambda_r=1.0,
        lambda_kd=1.0,
        lambda_orth=1.0
    )
    
    # Create mock outputs
    batch_size = 8
    outputs = {
        'fused_mu_teacher': torch.randn(batch_size, latent_dim),
        'fused_logvar_teacher': torch.randn(batch_size, latent_dim),
        'mu_vs': torch.randn(batch_size, latent_dim),
        'logvar_vs': torch.randn(batch_size, latent_dim),
        'mu_vr': torch.randn(batch_size, latent_dim),
        'logvar_vr': torch.randn(batch_size, latent_dim),
        'z_vs': torch.randn(batch_size, latent_dim),
        'z_vr': torch.randn(batch_size, latent_dim),
        'logits': torch.randn(batch_size, num_classes),
        'reconstructed_views': torch.randn(batch_size, 4, 128),
        'original_views': torch.randn(batch_size, 4, 128)
    }
    
    labels = torch.randint(0, num_classes, (batch_size,))
    
    # Test loss computation
    loss_pack = model.loss_function(
        outputs, labels,
        beta_t=1.0, beta_s=1.0, gamma_s=1.0, kappa=1.0, R=0.0
    )
    
    print("Loss components:")
    for key, value in loss_pack.items():
        if value is not None:
            print(f"  {key}: {value.item():.4f}")
    
    assert 'total_loss' in loss_pack
    assert loss_pack['total_loss'].requires_grad
    
    print("✓ Loss computation test passed")

def main():
    """Run all tests"""
    print("=" * 60)
    print("Testing Disentangled IBKD GNN Framework")
    print("=" * 60)
    
    try:
        test_hsic()
        test_student_networks()
        test_decoupled_attention()
        test_loss_computation()
        
        print("\n" + "=" * 60)
        print("🎉 All tests passed! Framework implementation is working correctly.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
