{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Mutual Information Estimation Quality\n", "\n", "In this experiment, we compare our CLUB estimator with other baselines on MI estimation quality. \n", "First, we draw samples from Gaussian and Cubic distributions with the true MI values pre-known. Then we compare different MI estimators on estimating MI values based on the generated samples. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import torch\n", "import matplotlib.pyplot as plt\n", "import torch.nn as nn\n", "import time\n", "\n", "#import os\n", "#os.environ['CUDA_VISIBLE_DEVICES'] = '2'\n", "\n", "torch.backends.cudnn.enabled = True\n", "torch.backends.cudnn.benchmark = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, we define the sampling function, which can sample Gaussian or Cubic data with the given [correlation coefficient](https://en.wikipedia.org/wiki/Mutual_information#Linear_correlation) $\\rho$:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def sample_correlated_gaussian(rho=0.5, dim=20, batch_size=128, to_cuda=False, cubic = False):\n", "    \"\"\"Generate samples from a correlated Gaussian distribution.\"\"\"\n", "    mean = [0,0]\n", "    cov = [[1.0, rho],[rho, 1.0]]\n", "    x, y = np.random.multivariate_normal(mean, cov, batch_size * dim).T\n", "\n", "    x = x.reshape(-1, dim)\n", "    y = y.reshape(-1, dim)\n", "\n", "    if cubic:\n", "        y = y ** 3\n", "\n", "    if to_cuda:\n", "        x = torch.from_numpy(x).float().cuda()\n", "        #x = torch.cat([x, torch.randn_like(x).cuda() * 0.3], dim=-1)\n", "        y = torch.from_numpy(y).float().cuda()\n", "    return x, y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Under the Gaussian distribution, the correlation coefficient and mutual information have [one-to-one](https://en.wikipedia.org/wiki/Mutual_information#Linear_correlation) mapping:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def rho_to_mi(rho, dim):\n", "    result = -dim / 2 * np.log(1 - rho **2)\n", "    return result\n", "\n", "\n", "def mi_to_rho(mi, dim):\n", "    result = np.sqrt(1 - np.exp(-2 * mi / dim))\n", "    return result"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["sample_dim = 20\n", "batch_size = 64\n", "hidden_size = 15\n", "learning_rate = 0.005\n", "training_steps = 4000\n", "\n", "cubic = False \n", "model_list = [\"NWJ\", \"MINE\", \"InfoNCE\",\"L1OutUB\",\"CLUB\",\"CLUBSample\"]\n", "\n", "mi_list = [2.0, 4.0, 6.0, 8.0, 10.0]\n", "\n", "total_steps = training_steps*len(mi_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Train different MI estimators with samples drawn from different Gaussian or Cubic distributions with different MI true values:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["finish training for NWJ with true MI value = 2.000000\n", "finish training for NWJ with true MI value = 4.000000\n", "finish training for NWJ with true MI value = 6.000000\n", "finish training for NWJ with true MI value = 8.000000\n", "finish training for NWJ with true MI value = 10.000000\n", "model NWJ average time cost is 0.004333 s\n", "finish training for MINE with true MI value = 2.000000\n", "finish training for MINE with true MI value = 4.000000\n", "finish training for MINE with true MI value = 6.000000\n", "finish training for MINE with true MI value = 8.000000\n", "finish training for MINE with true MI value = 10.000000\n", "model MINE average time cost is 0.003717 s\n", "finish training for InfoNCE with true MI value = 2.000000\n", "finish training for InfoNCE with true MI value = 4.000000\n", "finish training for InfoNCE with true MI value = 6.000000\n", "finish training for InfoNCE with true MI value = 8.000000\n", "finish training for InfoNCE with true MI value = 10.000000\n", "model InfoNCE average time cost is 0.004442 s\n", "finish training for L1OutUB with true MI value = 2.000000\n", "finish training for L1OutUB with true MI value = 4.000000\n", "finish training for L1OutUB with true MI value = 6.000000\n", "finish training for L1OutUB with true MI value = 8.000000\n", "finish training for L1OutUB with true MI value = 10.000000\n", "model L1OutUB average time cost is 0.005201 s\n", "finish training for CLUB with true MI value = 2.000000\n", "finish training for CLUB with true MI value = 4.000000\n", "finish training for CLUB with true MI value = 6.000000\n", "finish training for CLUB with true MI value = 8.000000\n", "finish training for CLUB with true MI value = 10.000000\n", "model CLUB average time cost is 0.004582 s\n", "finish training for CLUBSample with true MI value = 2.000000\n", "finish training for CLUBSample with true MI value = 4.000000\n", "finish training for CLUBSample with true MI value = 6.000000\n", "finish training for CLUBSample with true MI value = 8.000000\n", "finish training for CLUBSample with true MI value = 10.000000\n", "model CLUBSample average time cost is 0.004559 s\n"]}], "source": ["# train MI estimators with samples \n", "from mi_estimators import *\n", "\n", "mi_results = dict()\n", "for i, model_name in enumerate(model_list):\n", "    \n", "    model = eval(model_name)(sample_dim, sample_dim, hidden_size).cuda()\n", "    optimizer = torch.optim.Adam(model.parameters(), learning_rate)\n", "\n", "    mi_est_values = []\n", "\n", "    start_time = time.time()\n", "    for i, mi_value in enumerate(mi_list):\n", "        rho = mi_to_rho(mi_value, sample_dim)\n", "\n", "        for step in range(training_steps):\n", "            batch_x, batch_y = sample_correlated_gaussian(rho, dim=sample_dim, batch_size = batch_size, to_cuda = True, cubic = cubic)\n", "\n", "            model.eval()\n", "            mi_est_values.append(model(batch_x, batch_y).item())\n", "            \n", "            model.train() \n", "  \n", "            model_loss = model.learning_loss(batch_x, batch_y)\n", "           \n", "            optimizer.zero_grad()\n", "            model_loss.backward()\n", "            optimizer.step()\n", "            \n", "            del batch_x, batch_y\n", "            torch.cuda.empty_cache()\n", "\n", "        print(\"finish training for %s with true MI value = %f\" % (model.__class__.__name__, mi_value))\n", "        # torch.save(model.state_dict(), \"./model/%s_%d.pt\" % (model.__class__.__name__, int(mi_value)))\n", "        torch.cuda.empty_cache()\n", "    end_time = time.time()\n", "    time_cost = end_time - start_time\n", "    print(\"model %s average time cost is %f s\" % (model_name, time_cost/total_steps))\n", "    mi_results[model_name] = mi_est_values"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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\n", "text/plain": ["<Figure size 1339.2x244.8 with 6 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "import pandas as pd\n", "colors = sns.color_palette()\n", "\n", "EMA_SPAN = 200\n", "\n", "ncols = len(model_list)\n", "nrows = 1\n", "fig, axs = plt.subplots(nrows, ncols, figsize=(3.1 *ncols , 3.4 * nrows))\n", "axs = np.ravel(axs)\n", "\n", "\n", "xaxis = np.array(list(range(total_steps)))\n", "yaxis_mi = np.repeat(mi_list, training_steps)\n", "\n", "for i, model_name in enumerate(model_list):\n", "    plt.sca(axs[i])\n", "    p1 = plt.plot(mi_results[model_name], alpha=0.4, color=colors[0])[0]  #color = 5 or 0\n", "    mis_smooth = pd.Series(mi_results[model_name]).ewm(span=EMA_SPAN).mean()\n", "    \n", "    if i == 0:\n", "        plt.plot(mis_smooth, c=p1.get_color(), label='Estimated MI')\n", "        plt.plot(yaxis_mi, color='k', label='True MI')\n", "        plt.xlabel('Steps', fontsize= 14)\n", "        plt.ylabel('Mutual Information', fontsize = 14)\n", "        plt.legend(loc='upper left', prop={'size':15})\n", "    else:\n", "        plt.plot(mis_smooth, c=p1.get_color())\n", "        plt.yticks([])\n", "        plt.plot(yaxis_mi, color='k')\n", "        plt.xticks([])\n", "    \n", "    plt.ylim(0, 15.5)\n", "    plt.xlim(0, total_steps)   \n", "    plt.title(model_name, fontsize=15)\n", "    #plt.subplots_adjust( )\n", "\n", "plt.gcf().tight_layout()\n", "# plt.savefig('mi_est_Gaussian.pdf', bbox_inches=None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Calculate the bias, variance and mean-squared-error (MSE) of the estimated MI to the true MI values:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["bias_dict = dict()\n", "var_dict = dict()\n", "mse_dict = dict()\n", "for i, model_name in enumerate(model_list):\n", "    bias_list = []\n", "    var_list = []\n", "    mse_list = []\n", "    for j in range(len(mi_list)):\n", "        mi_est_values = mi_results[model_name][training_steps*(j+1)- 500:training_steps*(j+1)]\n", "        est_mean = np.mean(mi_est_values)\n", "        bias_list.append(np.abs(mi_list[j] - est_mean))\n", "        var_list.append(np.var(mi_est_values))\n", "        mse_list.append(bias_list[j]**2+ var_list[j])\n", "    bias_dict[model_name] = bias_list\n", "    var_dict[model_name] = var_list\n", "    mse_dict[model_name] = mse_list"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAbgAAAN5CAYAAAB64S6LAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAPYQAAD2EBqD+naQAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjMsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+AADFEAAAgAElEQVR4nOzdd3xUZdr/8c9MekI6SYD0EEhoIZBGC00QlC4gCCqwK3bFVVF59rfPrq7PrqAC1l1BBUWxINKESAfBBFKAAAECSUiH9F6nnN8fI6ORFpVkBrjer5d/zMyZM9egzpdzn/u+bpWiKApCCCHELUZt6gKEEEKItiABJ4QQ4pYkASeEEOKWJAEnhBDiliQBJ4QQ4pYkASeEEOKWJAEnhBDiliQBJ4QQ4pYkASeEEOKWJAEnhPhD9u3bh0qlIiAgwNSlCNGCpakLEOJmU15ezscff8yuXbs4efIk5eXlKIqCm5sbvXr1IjY2lmnTptGjRw9TlyrEbU0lvSiFaL13332XRYsWUVtbC4Cbmxs+Pj5YW1tTVFREXl6e8dhp06axbt06U5XabhITE3nwwQfx9vZm9+7dpi5HCCMJOCFa6fnnn+fNN98EYPr06SxatIjw8HBUKpXxmAsXLrB+/XqWLVtGWVkZlZWVpipXiNueBJwQrbB+/XqmTZsGwKuvvspf//rXax7f1NTEkiVL+Nvf/tYe5QkhrkAmmQhxHXq93hhUw4cPv264AdjY2FwWbrW1tXz++efMnj2bnj174uLigq2tLUFBQfzpT3/i1KlTVzxXayZx/OMf/0ClUjF37tzLXsvJyeHJJ58kJCQEOzs77Ozs8PPzY+jQofztb3/jwoULLY7XaDS8++67DB48GFdXV6ysrPD09CQsLIyHHnqIPXv2tLq+/Px83nnnHe6++26Cg4Oxt7fH0dGRsLAw/vrXv1JWVnbF77N69WpUKhXDhw8H4NNPP2XgwIE4OjrSoUMHBg0axDfffHPVPw8hAFCEENeUlJSkAAqgfPvtt7/7PFu2bFEAxcLCQunSpYvSv39/pWfPnoqDg4MCKLa2tkpcXNxl79u7d68CKP7+/lc999///ncFUObMmdPi+dTUVMXZ2VkBFBsbG6VHjx5KZGSk4u3trVhYWChAi8/U6XTKqFGjjN/Xz89PiYqKUrp166bY29srgDJjxoxW1/fcc88Zv1tAQIASFRWlBAcHK1ZWVsbz5+TkXPa+VatWKYAybNgw5aGHHlIAxdvbW4mIiFCcnJyM9b3//vvX/kMXtzUJOCGuY+nSpcYf1LKyst99njNnzijr1q1TqqurWzzf0NCgvP3224qFhYXi4eGh1NfXt3j9jwTc5MmTFUCZNWuWUlFR0eK1yspKZdWqVUpaWprxuY0bNxrD5OjRoy2O1+v1yr59+5Qvvvii1fXt2rVL2bNnj6LRaFo8X1RUpPzpT39SAGXcuHGXve9SwFlZWSmurq7Ktm3bjK81Nzcrjz32mAIojo6OSk1NzVX/XMTtTYYohbiO/Px8AFxcXHBzc/vd5wkJCWHatGk4Ojq2eN7W1pannnqKGTNmUFJSwnffffeH6v2l06dPA/DCCy/g4uLS4jVnZ2fmzp1Lz549Lzt++vTphIeHtzhepVIxbNgwZs6c2erPv+OOOxgxYgSWli1XJHl6evLhhx/i7e3Ntm3bKCoquuL7NRoNy5Yt46677jI+Z2VlxdKlS/Hw8KCmpoa9e/e2uh5xe5F1cEJcR3V1NQAdOnS46jEzZ87kq6++uuz5VatWtbgvptfriYuLY8eOHWRmZlJTU4NOpwMgNzcXgJSUFKZPn35Davf39yc9PZ21a9fSu3dvLCwsrns8wI4dOygqKsLLy+sP11BbW8u6des4ePAgBQUF1NXVofw0t62mpgZFUTh69Chjx4697L1OTk48+OCDlz1va2tLv3792LFjB+fOnfvDNYpbkwScENfh5OQEYFz7diWhoaEMHjzY+DglJYXGxsYWxxQXFzNhwgQSExOv+XlXm3jxeyxcuJBdu3axZMkSPv30U+68804GDBjAkCFD6NOnz2XHT548mZCQEE6dOoWfnx/Dhw9nyJAhDBw4kEGDBmFvb/+bPj8hIYGpU6deNpHl1672nbt3795iGcYvXQrfa/17Ebc3GaIU4jp8fHwAqKqqoqKi4orH/OMf/+DgwYPGf7y9vS87Zt68eSQmJuLn58eaNWvIysqioaEBxXAv3DjrUqPR3LDaR40axd69exk7dizl5eV8+umnPP7444SFhREUFMTKlStbHG9nZ8eBAwdYsGABLi4u7Nixg//93/9l9OjReHh4MH/+fEpLS1v12TU1NUyZMoULFy4wdOhQ4uLiuHDhAk1NTcbvHBsbe83v7ODgcNXzq9WGny+9Xt+qesTtR67ghLiOSz/CiqKwf/9+Jk+e/JvPUVRURFxcHABbtmwhLCzssmOudhVz6QpGucaS1ebm5qu+NnToUIYOHUpjYyNJSUkcPHiQjRs3kpiYyMMPP4xGo+Hxxx83Hu/h4cHy5ctZvnw56enpxMfHs3PnTjZs2MCHH35IWloaBw4cuO5wZ1xcHEVFRfj4+BAXF3fFq78bebUqxK/JFZwQ19G/f39CQ0MBePvtt3/XObKyslAUBVdX1yuGG0B8fPwVn790FVNcXHzV82dkZFy3BltbW2JjY1m0aBGHDx9mwYIFALzzzjtXfU9ISAjz5s1j7dq1xMfHo1KpSEhIIDk5+bqfl5mZCUBUVNQVw628vJz09PTrnkeI30sCTojrUKvVvPLKKwDs3buXxYsX/+ZzXPqBr6mpoa6u7rLXd+7cybFjx6743uDgYFQqFY2NjaSkpFz2ekFBAVu3bv3NNV1aRF1QUNCq4/v164ezs3Or33PpO1/t/tubb75pnGAjRFuQgBOiFaZPn84zzzwDwEsvvcSMGTM4evToZcOGNTU1fPjhh5ddbfXq1YuOHTui1Wp54oknqK+vN762Y8cOZs6cia2t7RU/28XFhaFDhwLw9NNPU1JSYnwtPT2dcePGodVqr/jeadOm8c0331wWqhcuXDD21YyKijI+/+abb7J48WLOnz/f4vjm5mYWL15MZWUlarWa/v37X/HzfulSgB46dIj//Oc/xj8rrVbLsmXLeO211676nYW4IUy0/k6Im9KyZcuMHT0Axc3NTenbt68yYMAAJTQ0VLG2tja+NmHChBZdOi4tXgYUJycnJSIiQvHz81MAJTw8XHn++eevuFhbURQlOTnZ+Lk2NjZKWFiYEhoaqqhUKqV///7Ks88+e8X3XupiYmFhoXTr1k2JiYlRQkNDjV1MPD09Wyz0XrBggbFGLy8vJSIiQgkPDzeeB1CWLFnS4jOutdB77ty5xvd16tRJiYyMVNzc3BRAmT9/vjJs2DAFUFatWtXifb/sZHI1c+bMUQDl73//+1WPEbc3uYIT4jd45plnyMnJYfHixYwaNQpbW1vOnDnDsWPHqK6uZujQofz973/nzJkzbN68GT8/P+N7586dy5YtWxgyZAharZbTp0/ToUMH/vd//5f4+PhrzhiMiIjgxx9/ZNKkSdjb25Oenm7skfnjjz9etnj8kjVr1vDkk0/St29fqqurSUlJIT8/n969e7No0SJOnjzZYqH3Y489xv/93/8xevRobG1tOX36NKdOncLZ2ZmZM2dy4MABFi5c2Oo/r48++og333yTnj17Ul5ezrlz5wgNDeXjjz9mxYoVrT6PEL+H7CYghBDiliRXcEIIIW5JEnBCCCFuSRJwQgghbkkScEIIIW5JEnBCCCFuSdKL8nfQ6/UUFhbi6Oh41U7nQgghbjxFUaipqaFLly7GhttXIwH3OxQWFuLr62vqMoQQ4raVl5dn3OnjaiTgfodLi2rz8vKMe4UJIYRoe9XV1fj6+l61ucEvScD9DpeGJZ2cnCTghBDCBFpze0gmmQghhLglScAJIYS4JUnACSGEaDffZ3/P8K+Gsz17e5t/lgScEEKIdlHWUMYr8a9Q1ljGywkvU9ZQ1qafJwEnhBCizSmKwj8P/ZN6rWGz33pNPa8eerVNP1MCTgghRJvbnr2d3bm70Sk6AHSKjl25u/g++/s2+0wJOCGEEG2qrKGMVxJeQUXLqf0qVLyS8EqbDVVKwAkhhGgzvxyaVGi5v7aC0qZDlRJwQggh2kxGZUaLoclfuzRUmVGRccM/WzqZCCGEaBMN2gZ25exCjRo9+iseY6GyYITvCIJdg2/450vACSGEuKEURSHufBzLjizjYt1FANQqNXqlZcipUOFg5cD/G/D/2qQOCTghhBA3zImSEyxOWkxqSSoAnRw68WzEs6DACwdeaHGsgsLfBv4Ndzv3NqlFAk4IIcQfdrHuIm8feZstWVsAsLO048+9/8ycXnOwtbRFURS252xnX94+dIrOODQ5NmBsm9UkASeEEOJ3a9A2sPrkalalraJB2wDAxK4TWdB/AZ72nsbjVCoVfxvwNy4eryci425Sum3j/01vm6HJSyTghBBC/GaKorDt/DaWpSyjqL4IgH6e/Xgh6gV6d+x9xffYaRwZljUDnQaGZc7ATuMIdm1XowScEEKI3yS1JJUlSUs4XnIcgC4OXfhL5F8Y4z/mqvu0KYrC/rVnUDSG5d6KBvZ/kc5dj/Rpszol4IQQQrTKxbqLLD+ynK1ZWwHDfbb5febzQM8HsLW0veZ7M1KKyTpWanys6CHraAnnkovoFunVJvVKwAkhhLimek09q9NWs+rkKhp1jQBM6jqJBf0X4GHvcf33Vzez7/P0K762f2063t1dsXeyvqE1gwScEEKIq9ArerZmbWX5keUU1xcD0N+zPy9Ev0Av916tOseloUlNk/aKrzc3attsqFICTgghxGWOFR9jSdISTpSeAMC7gzfPRjzLaP/RV73PdiVlBbUthiZ/7dJQZVlhLe5dOvzhun9JAk4IIYTRhdoLLDuyjLjzcQDYW9ozP8xwn83GwqbV51EUhdxT5RzamHnN41RqCOzrccPDDSTghBBCYLjP9vHJj1mdtpomXRMqVEwOnsxT/Z5q1X22Xyo8V8GhTVlcyKgCwMJaDXoFnVa57FhrW0uG3RdyQ77Dr0nACSHEbcx4ny1lOcUNhvtsEV4RvBD1Aj3de/6mcxVlV3N4cxZ5p8oBsLBU03uYN/3H+FNwtoIdH6Zd9p5hs0LaZIIJSMAJIcRt61jxMRYnLuZk2UnAcJ/tucjnGOU36jffZzu8OYvzqYZ7bWq1ih6DOxN5dwAdXA3LB4IjPMlILuL88VIU/c9Dk221RAAk4IQQ4rZTWFvI8pTlxGUb7rM5WDkwv8987u95/2+6z1ZZVE/id+c5l1wECqhU0D2mE1HjAnH2aNmiRKVSMWxWKPnph2hu0Lbp0OQlEnBCCHGbqNfU89HJj/gk7RPjfbZ7ut3Dk/2epKNdx1afp6a8kaSt5zmTcBFFb7iv1rW/B9ETgnDr7HDV99k7WTN8dggHvz5H7IzubTY0eYkEnBBC3OL0ip4tmVt468hblDSUABDpFcmL0S8S6hba6vPUVTWR8n0OaQcK0P80YcS/tzsxE4Pw8HNs1Tm6RXq16bDkL0nACSHELexI0REWJy3mVNkpAHw6+PB85POM9BvZ6vtsjbUajuzI4cTefLQaw6al3iEuxEzsSueuzm1W+x8lASeEELeggtoClqUsY3v2dsBwn+2RsEeY3WM21hatGxpsbtBybHceqbtyaW7UAeAV6ETMpCB8Q93arPYbRQJOCCFuIXWaOj46YbjP1qxvRoWKqd2n8kT4E62+z6Zp1nFiXz5Ht+fSWKcBwN2nAzETgwjo4/6bZliakgScEELcAvSKnk0Zm3j76NuUNhim60d3iuaFqBcIcWvdbEWdRk/awUJS4rKpr24GwMXLnugJgQT390Sl/uPBlp5wgD2rPmDkvEcJGTjkD5/vWiTghBDiJpdSlMLixMWcLj8NgK+jL89HPs8I3xGtutrS6/ScOXSRpK3nqS1vAsDR3ZaocYGExHihtlDfkDrrqyrZueJdmurr2LnyHXx79sbe2eWGnPtKJOCEEOImlV+Tz7KUZezI2QFAB6sOPNr3Ue4Lva9V99kUvUJGSjGJ352nsqgeAHtnayLvCqDnkC5YWN6YYANDb8qdK9+jubEBgOaGBnZ9+D4Tn/ufG/YZvyYBJ4QQN5k6TR0rj69kzak1NOubUavUTO1muM/mbud+3fcrisL51FISt2RRVlAHgK2DFf3H+tNnmDeW1hY3vOb0hANkJCX8XINez7nEeNITDhAyMPaGfx5IwAkhxE1Dp9exKXMTbx95m7LGMgBiOsewMHJhq+6zKYpC/ukKDm3Ooji7GgBrWwvCR/vR9w5frG3bJhLqqyrZtfI9QAX8suGyip0r38W3Z582GaqUgBNCiJtA0sUkXk963Xifzc/Rj+cjn2e47/BW3We7kFHJoU1ZFJ6rBMDSWk3YCF/63emHrYNVm9Xdcmjy17sJKG06VCkBJ4QQZiyvJo9lKcvYmbMTAEcrRx7p+wizQmdhZXH9YCrOMXT4z00zdPhXW6roPdSbiLEBbd4qC6AsL6fF0OSvXRqqLM3LoaOv/w39bAk4IYQwQ7XNtaw8YbjPptFrUKvUTO8+ncfDH8fN9vqLrMsKa0nccp6so4bWXCq1ih6DDB3+Hd1s27p8wHD1Vpqfh4WVNTpN8xWPUanVBEcOuOHhBhJwQghhVnR6HRszNvL20bcpbzRcdQ3oPIAXol6gm2u3676/qsTQ4f9soqHDPyroHuVF1PhAXDzt27j6n5XkZrN31QfknTpheEKlAuXXQ5QqrO3sGPXQ421SgwScEEKYiaSLSSxOXEx6RToAAU4BPB/5PEN9hl73PltNeSPJ27I5HX/B2OE/qJ8H0RMCce/Soc1rv6Shtob4rz8ndcc2FEWPpZU1UZOm4ezpxffvL/vV0Qqj5z/ZZmvhJOCEEMLE8qrzeDPlTXbn7gbA0dqRx/o+xsyQmde9z1Zf3UzK99mk/VCITmtohOzXy42YiUF4+ju1ee2X6PU6TuzewcGv1tBYY5ih2T1mMMMe+DNOHp4oikJG0iEyUw6j6PXGocm2WiIAEnBCCGEyNc01rDy+ks9Of4ZGr8FCZWG8z+Zq63rN9zbWaTi6M5fje/LQNhuCrUs3F2ImBdEluO26g1xJ/pk09qz6gJLsLADcffwYOe8R/Hr3NR6jUqkYPf8J8tKO01Rf16ZDk5dIwAkhRDvT6XV8m/Et7x5913ifbVCXQSyMXEiwa/A139vcqCV1dx7HduXR3KAFwNPf0dDhv4dbuzZCrikv5YfPVnHmx/0A2Dg4MPje++k7+m7UFpcvFrd3dmH0w08ae1G2ZZsukIATQoh2dfjCYZYkLeFsxVnAcJ9tYdRCYr1jrxlO2mYdJ/YXcGR7Do21P3X493YgekIQgX07tmuwaZubSdm6kcMbvkbT1AgqFWF3jGHwjAewd7r2/nAhA2PbdFjylyTghBCiHeRU5/Bm8pvszdsLgJO1E4+HP869Ifdipb76fTadVs/pHwtJ3pZNXZVhqr2zpx3REwLpFuF1Qzr8t5aiKGSmJLLv05VUFV0EoEtIT0bOfRivoGtfeZqCBJwQQrSh6uZqVqSu4PMzn6PVa7FQWTAjZAaP9X0MF9urD9HpdXrOJhaR+N15asoaAejgZkPUuEBCB3S6YR3+W6usII99n6wkO/WIoRZXN4be/ydCBw8z2/3hJOCEEKINaPVavj1nuM9W0VQBwGDvwbwQ+QJBLkFXfZ+iV8g4UkzSd+epuPhTh38nayLuCqDXkC5YWLVvsDXV15Ow/guOxm1Gr9NhYWlJxPgpxEy5F2tbu3at5beSgBNCiBvs0IVDLElawrmKcwAEOgeyMHIhsT5Xv/ekKAo5J8o4vCWL0rxaAGwcLOl/pz99Rvhg1QYd/q9F0etJ+2EPB9aupr7K0L8yKCKa4Q8+hGunLu1ay+8lASeEEL/B99nf89rh11gUs4gxAWNavJZTncMbyW+wL28fAM42zjze93Gmh0y/5n22/DPlHNqURdF5w/oxK1sLwkcZOvzb2LX/z/SFjHT2rPqAixmGiTCunb0ZMWc+gf0i272WP0ICTgghWqmsoYxX4l+hRlPDywkvE+kVibudO9XN1XyQ+gFrz6xFq9diqbJkZuhMHu37KM42V59VeDGrikObsihINwxhWlqp6TPCh/53+mPboe06/F9NXWUFB774hLR9uwCwsrVj4LT76H/XBCws27+eP0oCTgghWkFRFP556J/Uaw33xeo19bxy6BUGdR7Ee8feM95ni/WO5fmo5wlyvvp9tpK8Gg5vziLnhGFPN7WFil6x3kTc5Y+Ds03bf5lf0Wk1HP3+OxK++YLmBsP36zXsDobcN4cOrtdv7GyuJOCEEKIVtmdvN7bSAtApOvbk7mFP7h4AgpyDWBi1kCHeQ656joqLdRzefJ7MI8WAocN/6MBORN4dgJO7aSZsZKceYe/qFZQX5gPgFdSNkfMeoUv3UJPUcyPdtgFXUFDAiy++SFxcHA0NDXTv3p2PPvqIiIgIU5cmhDAzZQ1lvJLwCipUKJdt2gkL+i9gbq+5WKqv/JNaXdpA0nfnST980dBQXwXdIr2IHh+Ii1f7dfj/pcqii+z79EMykw8BYOfkTOysOfQeNgqVun1naraV2zLgKioqGDx4MCNGjCAuLg5PT08yMzNxcWnf/m1CCPN3aWiyTlt3xXBTq9SklaZdMdxqK5pIjsvm9MFC9D91+A/s25GYiUG4e7dfh/9f0jQ2cnjjOpK/+xadRoPawoJ+Y8czYOp92DqYpqa2clsG3OLFi/H19WXVqlXG5wICAq56fFNTE01NTcbH1dXVbVmeEMKMnC0/22JosmtpOIOzp3IwcD1Z7sfQK3p25e4ioyLD2EeyoaaZlO05nNxfgE5jaITs29PQ4d8roP06/P+Soiikx//A/s8+prbccO/Pr084I+c+jLuPn0lqamu3ZcBt3ryZMWPGMH36dPbv34+3tzePP/448+fPv+Lx//73v3n55ZfbuUohhKklXUzitcOvGR/bajowNGsG1jo7hmXOoNApA411AyN8RxDsGkxTvYZju/JI3Z2HpkkHQOdgZwZMCqJLt2vvDtCWirOz2LPqAwrOpAHg5OHF8DkPERw5wGy7kNwIKkW5bIvVW56trWG79meffZbp06eTmJjIM888wwcffMCDDz542fFXuoLz9fWlqqoKJyfT/G1MCNF2CmoLWJq8lB05OwBwsHJAq9My7NRsAip6o8YCPTqy3U5wqM961t+1gYJD9RzdkUtTvaHDv4efocO/X8/27fD/Sw011fz41Wcc3/W9YfNRaxtiJk8nYsIUrKzbf7bmjVBdXY2zs3Orfn9vy4CztrYmMjKS+Ph443NPP/00SUlJJCQkXPf9v+UPWAhx86jX1PPRyY9YfXI1zfpm1Co107tP54nwJ9i9J4mizZcPenXorUOXY0tDjaHDv1sXB2ImBBEY3r4d/n9Jr9ORuiuO+K8+o7HO0BUlZGAsQ+//E04dPUxS043yW35/b8shys6dO9OzZ88Wz/Xo0YP169ebqCIhhCkpisLW81tZlrKM4nrDFP7oTtG8EPUCIW4h1Fc3U7HTFgUNKn4OLQWF2pMWgAYnDzuixwfSLcoLdTt2+P+1vLTj7Fm9gtLcbAA8/AIYMe8RfHv2MVlNpnJbBtzgwYNJT09v8dzZs2fx9/c3UUVCCFM5UXKC15Je43jJcQC8O3izMHIhI/1GolKpUBSF/WvPoGnStgg3wPi4o28Hpr0UiUU7d/j/perSYvZ/toqzCQcAsO3gyOAZDxB2x5grbj56O7gtA+4vf/kLgwYN4l//+hf33nsviYmJrFixghUrVpi6NCFEOympL2H5keVsztwMgJ2lHQ+HPcwDPR/AxuLn+1PlhXVkHSu95rlK82qpLKrHvUv7T7PXNDeRvOVbEjd+g7a5CZVKTdjouxh872zsHG/vWyi3ZcBFRUWxYcMGFi1axCuvvEJgYCDLly9n9uzZpi5NCNHGmnRNrDm1hpXHVxrbbk3sOpEF/Rfgae952fFajQ7bDlbGXbR/TaWGwL4e7R5uiqKQkZTAvk8/orqkCACfHr0ZMfdhPAOu3ibsdnJbBhzA+PHjGT9+vKnLEEK0E0VR2JO7h9eTX6egtgCAMI8wXop6iT4el9+fqiqp59CmLDKSi695XmtbS4bdF9ImNV9NWX4ue1avIPfEMQA6uHdk2P1/ImRg7C097f+3um0DTghx+zhbcZYliUs4fPEwAJ52nvwl8i/cHXg3alXL+2b11c0kb8sm7YcCQ/cRFYREd8IzwJEDX5277NzDZoVg72TdLt+jsa6WhG++4Oj3W1D0eiysrIiacA/Rk6Zj9dPyJ/EzCTghxC2rorGC9469x7qz69AreqzV1sztPZc/9/4z9lYte0A2N2pJ3Z3H0R25xkXafr3cGDilKx19HFEUhYL0Cs4fL0XR/zw02S3Sq82/h16v4+TeXRz88lMaqqsACI4awLAHHsLFq1Obf/7NSgJOCHHL0eg1fJ3+Ne8de4+a5hoARvuP5rnI5/Du4N3iWJ1Oz6kDhSRtPW9cy+bp78jAKV3xCf15qxiVSsWwWaHkpx+iuUHbbkOThWdPs2fVBxRlZQDg1sWHEXMfJqBv/zb/7JudBJwQ4pbyY8GPLElaQlZVFgAhriG8GP0iUZ2iWhynKAqZR0o4tDGTqpIGAJw87BgwKYjgCM8r3suyd7Jm+OwQDn59jtgZ3dt0aLK2opwDn6/i1IG9AFjb2TNo+izCx4zHwlJ+ultD/pSEELeE7Kps3kh+g/35+wFwtXHlqf5PcU/wPVioW64DK0ivIP7bDIpzDFd3do5WRI0LpGdsl+uuZesW6dWmw5JajYYj2zZx6Nuv0DQ2gEpF7+GjGDLzQRxcTNfP8mYkASeEuKnVNNfwQeoHfH7mc7R6LZYqS2b1mMUjfR/BybrlOrDS/FoSNmSSm2bopm9pY0G/0X6Ej/LF2tb0P4dZR5PY98lKKi4UAtA5OISR8x6hU3B3E1d2czL9v1EhhPgddHodGzM28vbRtylvLG8Q+M8AACAASURBVAcg1juWhVELCXQObHFsdVkDiZvPk554ERRQq1X0iu1C5LjAdpsBeS0VFwrY9+mHZB1JAsDe2YWhs+fRM3bELbP5qClIwAkhbjrJF5NZnLSYM+VnAAhwCuCFqBeI9YltcVxjrYbk77M5sS8fvdbQVz440pOYiUG4eJpmJ+1fam6o59CGr0n5biN6nRa1hSX9757IgHtmYmNv+vpudhJwQoibRmFtIUtTlrI9ezsAjlaOPBb+GDNDZ2KltjIep2nWcXxPHke259LcYNi+xjvElUH3dMXT3/TtqxRF4fTBffzw+SrqKgxXnwHhEYyYMx+3Lj4mru7WIQEnhDB79Zp6Pj75MavTVtOka0KtUjO121Se7PckbrY/T+XX6/ScOXSRxC3nqas07OHo7tOBQVO64mvCfdl+qSgrg92r/suFs4arTxevzgyfM5+g/lFmUd+tRAJOCGG2FEVh2/ltLE1ZatzGJqpTFC9GvUiIW0iL486nlnJoYyYVFw39JR3dbImZFET3KC9UJty+5pL6qkoOfvkpJ/buBEXBysaWmHtmEDFuMpZWVtc/gfjNJOCEEGbpZOlJFicu5liJod+idwdvnot8jlF+o1pc6VzIqCRhQyYXMg0dPmwcLIm8K4A+w3ywsDL9BA2dVkvqjq3Er1tLU30dAD2GDCd29lwc3TqauLpbmwScEMKslNSX8NaRt9iUuQkwbGPzUJ+HeLDng9ha/txvsfxCHYc2ZnI+1bCVjaWVmr53+NJvjD82dubx05Zz4hh7V6+gLD8XAM+AroyY9zA+ob1MXNntwTz+KxBC3Paadc2sObWGFcdXGLexmRA0gQX9F+Dl8PPC6tqKJpK+y+J0/AUUBVQq6DG4C9HjA3Fwsbna6dtVVXER+9d8xLnEeABsHZ2InfkgvUeORq2+PTcfNQUJOCGESSmKwp68PbyR9Ab5tfkA9OnYh5eiXyLMI8x4XFO9hiPbc0ndk4dOowcgsG9HBk7pimsnh3arNz3hAHtWfcDIeY8SMnBIi9c0TY0kblpP8ub1aDXNqNRqwu8cx6Dps7Ht0P6bod7uJOCEECZzruIci5MWc/iCYRsbDzsP/hLxF8YFjTNuY6PT6DmxP5/kuGya6gxT/jt3dWbgPcF07urcrvXWV1Wyc8W7NNXXsXPlO/j27I29swuKonD20I/sX/MRNWUlAPj2CmPE3Ifx8Ato1xrFzyTghBDtrrKxknePvdtiG5s5vebwUJ+HjNvY6PUK5xIvcnjzeWrKGwFw7ezAwCldCejj3u5T6hVFYefK92huNDRmbm5oYNeH7zNw+iz2rvqAvFMnAHDs6MHwB/5Mt5jBMu3fxCTghBDt5tI2Nu8fe5/q5mrAsI3NsxHP4uNoWOCsKAq5p8pJ+DaTsoJaABxcbIieEEjogE6or9MMua2kJxwgIynB+FjR6zmXGM+5xARAwdLKmqhJU4maOBUrG9l81BxIwAkh2kV8QTxLkpaQWZUJQHfX7rwY9SLRnaONxxRlV5OwIYOC9EoArO0siRjrT9gIHyytTTc5o76qkl0r3wNUgPKrVxUC+0dyx7zHcPZs+81PRetJwAkh2lROdQ5vJL3Bvvx9ALjYuPBUv6eY2m2qcRubyuJ6Dm3MIvOIYTG32lJF2HAfIu4KwNbBtIugWw5N/jrcDBuhWlpaS7iZIQk4IUSbqG2uZcXxFaw5vca4jc3M0Jk82vdRnG0Mk0Pqq5tJ2nqeUwcK0esVUEFoTCeiJgTi5G5n4m9gUJaX02Jo8tcUReFcYjyleTl09PVvx8rE9UjACSFuKJ1ex6bMTbx15C3jNjaDvQfzQtQLBDkHAdDcqOXYzlyO7spD26QDwL+3OwOndMXd23ym01dcLCR+/ZfXPEalVhMcOUDCzQxJwAkhbpgjRUd4LfE1TpefBgzb2CyMWshQn6EA6LR60g4UkrztPA01GgA8A5wYNKUr3iHms1t1fVUlCeu/4Piu79HrDAGstrBEr9P+6kgV1nZ2jHro8fYvUlyXBJwQ4g+7UHuBpSlL+T77ewA6WHXgsb6PcV/ofVhZWKHoFTKOFHNoUxbVJYZp9s6edgyY1JWu/T3MZjp9c2MDyVs2kPzdBjQ/LQcIDI9gyH1zKC/MZ+tbS371DoXR85/E3tml/YsV1yUBJ4T43Rq0Daw6uYqPT35Mk64JFSqmdp/Kk+FP4m7nDkD+mXLiv82kJLcGADsna6LHB9JjcGcsTDTl/9d0Wi0ndm8nYf0X1FcZZnB6BXVj6Ox5+PU2dFPx8A8kPf4AmSmHUfR649BkyMDYa51amJAEnBDiN1MUhbjzcSxNWUpRfREAEV4RvBT9EqFuoQCU5teQ8G0muacM9+GsbCzod6cffe/wxdrWPH56LnUgOfjlJ1RevAAY9mcbct+DdB8wpMWVpUqlYvT8J8hLO05TfZ0MTd4EzOO/MiHETSOtNI3FSYs5WnwUgC4OXXgu8jlG+49GpVJRXdrA4S1ZnE0sAgXUFip6D/Um4q4A7J2sTVz9z/LSjvPD2tVczDgLgJ2TMwOn3UfYHWOwsLzy0gR7ZxdGP/yksRelDE2aNwk4IUSrlDaUGraxydiEgoKdpR1/7v1n5vSag62lLQ21zaRsy+HED/notYb1Yt2ivIiZGIizh72Jq/9ZSW42B9au5vzRZACsbGyJnDCFyPFTsLa7fp0hA2NlWPImIQEnhLimZl0zn53+jBXHV1CnMWzYOT5oPAv6L6CTQyc0zTqS47I5uj2H5kbDjEOfUFcGTumKp7+TKUtvobq0mPivPyfthz2gKKgtLOhzx1gGTp2Jg4v5zOAUN44EnBDiihRFYW/eXt5IfoO8mjwAerv35sXoFwn3DEev05N2oIDE785TX9UMQEffDgyaEoxvTzdTlt5CQ20NiRvXcfT7Leg0hqUJ3QcMYcjMB3Dt7G3i6kRbkoATQlzmXMU5liQt4dCFQwB0tOvIXyL+wvig8ahQkXW0hISNmVQWGTYmdXS3ZcCkILpFeqFSm8eUf01zE0fjtpC4aR1NdYYrT9+efYidPZfOwSEmrk60Bwk4IW4z32d/z2uHX2NRzCLGBIxp8VplYyXvHXuPdWfXoVN0WKmtjNvYOFg5UJhRScK3GVzMMuwEYNvBisi7A+gd642FlXlM+dfrdZzav4cf131ObVkpAB39Ahg6ay4B4RFms+ZOtD0JOCFuI2UNZbwS/wo1mhpeTniZSK9I3O3c0eq1fJ3+Ne8de8+4jc0ov1E8G/ksvo6+lBfWsW/jcbKPGwLD0lpN+Cg/+o32w9rOPH5GFEUh60gSB9aupiw/FzDszTb43vvpETsctdp0uxEI0zCP/zKFEG1OURT+eeif1GsNw4r1mnpePfQq00Om83rS62RUZgDQzbUbL0a9SEznGGorGtnz6WnOJFxAUUClVtFzcGeixgfi4Gxjyq/TQuHZMxxYu5r80ycBsHXoQMyUewkfMx5La/NZmiDalwScELeJ7dnb2Z272/hYp+jYlbuLXbm7AMM2Nk+GP8nU7lPRNigkbMggdU8+Oo0egK79PIiZFIRrJweT1H8l5YX5HPzyU84djgfA0sqafndPJHriNGw7mE/TZmEaEnBC3AbKGsp4JeEVVKgIKu3L4OypHAxcT5b7MQCmdZvGMxHP4KDuwIldBaR8n01TvaGxcOdgZwbdE0ynIGdTfoUWaivKSfhmLSf27DC0zVKp6TX8DgZOm4VTRw9TlyfMhFkHXH19PaWlpbi7u+Pg8PPfGquqqnjttdc4ceIE/v7+PP/88wQGBpqwUiHM1y+HJm00DgzNmoG1zo5hmTModMqg2bqeyoZKLhyp5/Dmk9RWNAHg1sWBgZO74t/H3WwmZjTV15O8ZT3JWzeibTLUGRQRTezMB+noF2Da4oTZMeuAe/XVV1m8eDGHDx8mMjISgObmZgYOHEh6ejqKYuiWsH79elJTU/Hykh11hfi1jMoMw9CkAndk3ouVzgYVKqx0NgzNnM4Zr8O4Hotgd71hi5sOrjZETwgiZEAn1GYy5V+n1ZC6M45D67+kocYwCaZztxCGzpqHT8/eJq5OmCuzDrjdu3cTGBhoDDeAtWvXcubMGUaOHMmLL75IXFwcy5cvZ9myZbz22msmrFYI83O24ixvJL0BQNeyfgRV9DW+psaCoIpwgirCAbCxt6T/WH/ChvtgaW0eMw4VvZ70hAMc/GoNVUUXAXDt7E3sfXMIjh5oNleWwjyplEuXQWaoc+fO9OvXj23bthmfmzx5Mt999x3Z2dn4+PgA0KNHDywtLTlx4kS71FVdXY2zszNVVVU4OZlPKyIhLiltKOXdo++yIWMDekWPg8aZe48uwlpng4qW69UUFLoN6ciwKT2xdbhyk2FTyDlxjANrV1OUZZjd6eDiysBps+gz8k7UFuYRwKL9/ZbfX7O+gquoqMDVtWWPuPj4ePr06WMMN4CwsDC2b9/e3uUJYXYatY18eupTPjrxkXE5wGi/0QxOm8FFfS1w+RWPSgX6OrXZhFvR+UwOrF1NznHDbgXWdnZETZhKxLjJWNnamrg6cTMx64Dr1KkThYWFxsdpaWmUlpYya9asFsfJMIW43ekVPVuztvLWkbeM+7P1du/NwqiFdKoOZMNXR7lSuAGgGFpvlRXW4t7FdFPrq4ov8uNXn3H64D4A1BaWhN95NzH3zMDeyXxmcIqbh1kHXL9+/di6dSvHjh0jPDycZcuWoVKpGD9+fIvjzp07R5cuXUxUpRCmlVKUwutJr5NWlgZAZ4fOLOi/gJGdRnN8Vz5bdqVe8/0qNQT29TBZuNVXV3F4w9ek7tiKTmtYmhA6eBiDZzyAi1cnk9Qkbg1mHXAvvfQSW7ZsITIyEmdnZyoqKggPD2fkyJHGY4qLi0lNTeW+++4zYaVCtL/c6lyWpSwzLtS2t7Rnfth8Zna9j3MHy/j8vUM01RkCo6OPA5XFDWib9Zedx9rWkmH3tX/zYU1TI0e2bSZx0zc0NxiGU/36hDN01ly8goLbvR5x6zHrgIuJiWHTpk28/vrrlJSUMG7cOP71r3+hVv98k3zt2rU4OjoyduxYE1YqRPupaqriv6n/5cv0L9HqtahVaqZ2m8qjfR6j+Egz37x8lLqftq9x7WRPzKQggsI9yEgpZseHaZedb9iskHbdaVuv03Fy307i162lrqIcAM+ArsTOnktAWL92q0Pc+sx6FqW5klmUwhQ0Og1fpn/Jf1P/a2yIPNh7MM/2exYynTi85TzVJQ0AOLrZEj0hkO4xP69lUxSF7z84wfnjpSj6n4cm73qkT7vUrygKGcmHOLj2E8oL8wFw8vBiyMwHCB00FJXaPHYjEObtlplFKYQwBMOevD0sTV5Kbo2hS36wSzDPRTyHd1kIh9/PoqzAEBh2jobta3oNuXz7GpVKxbBZoeSnH6K5QduuQ5MFZ07xw+erKDxrWExu6+jEwHtmEDb6biytzGP2prj1SMAB//73v/mf//kfFixYwPLly01djhBGaWVpvJ70OilFKQC42brxVL+niNYPJ2ltNqlZhrWf1naW9LvTj74jfbGyufoaMXsna4bPDuHg1+eIndG9zYcmy/JzOfDFJ2QmHwbA0tqGiHGTiZp4Dzb25tO0WdyazD7g6uvrWbp0KZs2beLcuXPU1NRc8TiVSoX2pxlYv0VSUhIrVqwgLCzsj5YqxA1zse4ibx95my1ZWwCwsbDhwZ4PMslpBqlbC9ly+jhg2JctbKQv/Ub7tXodW7dIL7pFtm1bu5ryUuK/Xkvavl0oih6VWk2fEXcycNp9dHBzb9PPFuISsw64qqoqYmNjSUtLw8LCAmtraxRFoXPnzly8eNHYi9Lf3/93nb+2tpbZs2ezcuVKXn311RtZuhC/S52mjo9PfswnaZ/QpDM0Ex4fNJ65nR8ha3cN3x01TBJRW6joFetNxF3+ZrUvW2NdLUmb13Nk22a0zYb6g6MGMuS+B3H39jVxdeJ2Y9YB99prr3Hy5EkeeeQRli1bxqOPPsqaNWsoKCigsbGRr7/+mpdeeomYmBi++OKL33z+J554gnHjxjFq1KhrBlxTUxNNP3UuB8NNTiFuJJ1ex8aMjbxz9B3KGssA6O/Zn6eCn6XqRyt2r81CUQAVhMR0Inp8IE4d7Uxb9C9oNRpSd2zl0Ldf0VhrGGXpEtKTobPn4R3Sw8TViduVWQfcxo0b6dKlC2+//TZWVlYtOpbY2try4IMPEhkZSb9+/XjjjTdYuHBhq8/95ZdfcuTIEZKSkq577L///W9efvnl3/UdhLie+MJ43kh+g3MV5wDwdfTl6ZBnsT/hS9LyQvQ6w0hFULgH0RMDTdpt5NcUvZ7TP+7nx6/WUF1SDIC7jx+xs+YQ1D9augwJkzLrgMvJyWHUqFFY/TTL6tL6N41GY3yuZ8+eDBs2jE8++aTVAZeXl8eCBQvYsWMHtq3obbdo0SKeffZZ4+Pq6mp8fWW4RfwxmZWZvJH8BgcLDgLgZO3EIyGP0TU7hpMfFKBtLgDAt4crMZO64hVgPktSFEUhJ/UIP6xdTUnOeQA6uLkz6N7Z9Bp6hzRDFmbBrAPO1tYWG5uf7y9cWvNw8eLFFgHj5ubGjz/+2OrzpqSkUFxcTEREhPE5nU7HDz/8wLvvvktTUxMWv/gf1MbGpkUdQvwRZQ1lvH/sfdafW49O0WGpsmRm11kMqZjA6c+KOVafB4BXoBMDJnfFJ8T1OmdsXxczz3Fg7SpyTxomutjYOxA9eTr9xo7HykaaIQvzYdYB5+vrS05OjvFxaGgoAPv37+f+++8HQKvVkpSUhLt762dm3XHHHZdtrTNv3jxCQ0N58cUXW4SbEDdKk66JNafW8OGJD6nT1AEwsssdTNHOI2tTDceqDY3F3bo4MGBSEAFhHc1qiK/y4gUOfvkp6QkHALCwtCR87ARiJk/HztF8ri6FuMSsAy42NpaPP/6YqqoqnJ2dmTBhAgsWLODpp5+mpqYGHx8fPvroI7Kzsy/bYeBaHB0d6d275S7ADg4OuLu7X/a8EH+UoijEnY/jrSNvUVhnCLGebr2YY/U0pfsgrcwwqcSpoy3RE4LoFuVlNjtpA9RXVZKw/kuO74pDr9OBSkXP2BEMvvd+nDw8TV2eEFdl1gE3c+ZMUlJSSEhIYOzYsXTp0oV//etfPP/88zz55JOA4cejU6dOLF682MTVCnG5Y8XHeD3pdY6XGobzvOy8mO/4LLrDbpy/aGgwbO9sTdS4QHoM6oyFpfm0q2pubCDlu40kbfkWTaOhBVhgeARD7puDZ0CQiasT4vpuyl6Uhw8fZsOGDVRUVNC9e3fmzZuHm5tbu32+9KIU15NXk8eylGXszNkJgJ2FHfNcnsLteHdKcw3DkzYOlvQf40+f4T5YWbffsHh6wgH2rPqAkfMeJWTgkMte12m1nNizg4Rv1lJfVQmAV1A3hs6eh19vaYggTOu3/P7elAFnahJw4mqqm6tZeXwln5/+HI1eg1qlZrrzg4ScG0JxhiHYLG0sCL/Dl/DRftjYte8gSn1VJR8/8whN9XXYODjwp2UfYO/sAhhGQ84d/pGDX35KxQXDUKqLV2eG3Pcg3QcMMav7geL2Jc2WhWhnGr2Gdenr+E/qf6hsMlz1jLAfy+DCKZTGN1JMHWpLFX2G+tB/rH+7bk9ziaIo7Fz5Hs0/DTc2NzSw68P3mfjc/5B36gQ/fL6KixlnAbBzcmbgtPsIu2MMFpbSDFncnMwq4HJzDZ3Svb29sbCwMD5uLT8/v7YoS4irUhSFfXn7WJqylOzqbAB6WfVjQtk8Kg8plCqNqFQQOqgzUeMCcXQz3TT69IQDZCQlGB8rej3nEuNZ89ICis9nAmBlY0vkhClEjp+CtZ29qUoV4oYwq4ALCAhArVZz6tQpunfvTkBAQKuHRX5vs2Uhfq/TZad5I/kNEi8mAuCt8md6zWM0p9lTqTeM/AdHeBI9IRDXTqbtnF9fVcmule8BKqDlXYni85mo1GrCRt3FwKkzcXAxr3V3QvxeZhVwQ4cORaVSYW9v3+KxEOakqK6Id46+w+bMzSgoOOpcmNn4JNanPWnUKICCXy93BkwKwsPP0dTl/mpo8sq33P1692XUnx9r38KEaGNmFXD79u275mMhTKleU8/qtNWsTltNg7YBK50NUxvm0/FcCNpGPToUOnd1ZsDkrnTp5mLqco3K8nJaDE1eSc7xo5Tm5dDR9/ftzCGEOTKrgBPCHOn0OjZnbuado+9Q0lCChd6SsbWzCD4/AG29ghY97j4dGDApCP/e7mY16lB0PpPDG76+5jEqtZrgyAESbuKWIwEnxDUcunCIN5LeIL0iHZWiZmDVXUTkj0Fbo0KLgrOnHTETgwju74nKTLqPKIpCwek0Dm/8muzUI8bn1RYWhk4kLaiwtrNj1EOPt2+RQrQDsw246upqtFrtZQu4c3JyePfdd0lPT8fBwYFx48YZ+1IKcaNkVWWxNHkp+/P3g6KiV9VAhl6YilJphRbo4GpD1LhAQgZ2wsLCPLqPKIpC1pEkEjeuo/DsacBwdRY6aCjRk6ZRmp/L1reW/PpdjJ7/pHEtnBC3ErMLuP3797NgwQJjM+SOHTvy17/+laeffpoffviBsWPH0tTUZNzN++uvv2b9+vVs2LDBlGWLW0RFYwXvH3ufdWfXodPrCKjqzeiiWViUO6AAth2siBjrT+9h3lhamUdTbr1OR3rCARI3fUNpbjYAFlZW9B4+mqiJ9+Ds2QkAd19/0uMPkJlyGEWvNw5NhgyMNWH1QrQds+pkcvLkSSIjI2lubm7xvEqlYu3atSxcuJCqqioeeeQRAgMDycrKYsWKFdTW1vLxxx8zZ86cdqlTOpncepp1zXx++nNWHl9JjaaGTtVB3Fk0G/vSjgBY2VrQb7Qffe/wxdrWPP5eqG1uJm3/LpK2fEtV0UUArO3s6Dv6biLGTb7idP9rdTIR4mZw07bquv/++1m7di0PPPAA//jHP/Dy8iI5OZmHH36YqqoqSktLSU5Opm/fvsb3HD16lKioKIYNG8bu3bvbpU4JuFuHoihsz9nO8pTlFNQW4F7nzcgLM3AvMUy4sLBS02e4D/3H+GHXof27j1xJc0M9qTvjSNm6kbrKCgDsHJ3of/ckwseMw9bh2jt+X68XpRDm7KYNOD8/P9RqNZmZmS32ZNu5cydjxoxh0KBBHDx48LL3DRkyhPT0dEpKStqlTgm4W0NqSSqvJ71Oakkqzg0exBbeg09xTwDUahU9Bncm8u5AOriax2a39dVVHI3bzNHt39FUZ+hr6ejuQeSEe+gzcrRsNipuCzdtL8qioiLGjx9/2YajMTExAPj7X3kas7+/P4cPH27z+sStoaC2gLdS3iIuO44OTa6MLLif7sWRoKhABd0ivYieEIiLp3m0qqouLSHluw0c37MdbVMTAG5dfIiaNI0eQ4ZJr0ghrsKsAk6j0eDs7HzZ85dS2sbmyn+Ttra2Rq/Xt2lt4uZX01zDhyc+5LNTn6FusmZQwRT6FA1FpTfMggwI60jMxCA6+lx7iK+9lBfmk7jpG04f2IdeZ2hD5xXUjZjJ0wmOGoBKbR6zN4UwV2YVcEK0Ba1eyzdnv+H9Y+9TV9dAWOEd9Cu6Awut4cqnSzcXBkzuSueul//lyhSKsjJI3LiOs4nx8NMdBN9eYURPno5/n3CzWkguhDmTgBO3LEVROFBwgDeT3yS3PI9eF2OJuHAn1ho7ADz9HRkwqSs+PVxNHhqKopB/6gSHN64j5/hR4/NdIwcQPWkaXbqHmrA6IW5OZjXJRK1W/6EfGt1lXRrahkwyMX/p5emGTv8FiYQWDySq4C7smg2Nj1072RMzKYigcA/TB5teT+aRJBI3fs2Fc+nAT4uzBw8jeuJUOvoFmLQ+IczNTTvJBOD35q2pf6hE+/k++3teO/wai2IWMSZgTIvXSupLePfYu2w8u5Gupf2Ymf9XnBoNa9kc3WyJnhBI95hOqE3cVkuv05Ee/4NhcXZeDvDT4uwRdxI1YYpxcbYQ4vczq4CTiSLiesoayli96VvGZTzDqqJviJwfibudOw3aBj5J+4SPT3yMZ0lXpuYuxL2hCwB2jlZE3h1AryHeWFiZdmKGtrmZk/t2kbxlPVXFRcBPi7PvHEfE3ZNkLzYhbiCzCjghrkVRFP61bzHRZydirbMjOn0S/9q3mGHdh/DWkbewvODEmNxH6FQbCIC1nSX97vSj70hfrGxM21arqb6e1J3bOLJt08+Ls52cibh7En3vvPu6i7OFEL+dBJy4aXx/fjv80BkrnQ0qVFjpbOBAJ5ZnfUBM7jR8qkIAsLRWEzbSl36j/bB1MO0asfrqKo5s28yxHb9YnN3Rg6gJ99B7hCzOFqItScCJm0JZQxmfbv6WIRUzjc+psSCoPJyg8nDDYwsVvWK9ibjLHwdn03YfqS4tJnnLBk7s2YG2+efF2dGTpxM6eBgWlvK/nhBtTf4vE2bv0tBk1LmJKOhR0fI+moJCrW8hjz8yDaeOdiaq0qCsII+kTes5fXCvce+1Tl27ET15OsGRsjhbiPYkASfMXmpx6i+GJi8PCAWFkroSii0KcCLYBBXCxcxzJG5cx7mkBOPibL/eYURPuhe/Pn1llq8QJiABJ8xWs66Zr9O/5psftjK6Yv5Vj1OjJqg8HNeGTtCOkxAVRSEv7QSJm1ouzg6OGkD05Ol0Dg5pv2KEEJeRgBNmR6/o2XZ+G/9J/i9uGcHE5s+69vHo8Qtzxb1L+8xEVPR6MlMSSdy4jgsZPy/O7jF4GFGTptHR98pNwYUQ7UsCTpgNRVFIKExgefJbqM64Miz/zzhoDP0hnT3tqK6oR69RWgxTKuixtFEx+v4+bV6fXqfjTPwPJG5cR1l+LgCWVtb0HjmayPH34Ozp1eY1CCFaTwJOmIW0sjSWJS+n/ISW/8/efYdHcd37H3/Pc48vVQAAIABJREFUFnWtUBeqSCCaRDO99yKKa9wS+zqJ7RvHuBCcxC03sf27hsRJfJ1rXIKT6xTHDi5gerNpxpiOEE0SIIp6A7TSSto28/tjxYIQHa1W5ft6Hj1iZ2Z3zsrWfjRnzvmeQfn3EGJ1VR8JCvdl6KwUug+J4dieUtb/9XCj5ynomPRwGgEmzy1GardZObTRtXK2ufz85OwA+k+dwW0Zt8vkbCFaKQk44VWnzad5e+/bHNmXz5DTMxnQUH3EL9jA4OkppI2ORW9wXbGlDormyM5CTh84gw49Kk4S+4aROsgzV07WWguZ61yTs2urzgEXJmf3nzoD34BAj5xXCNE8JOCEV1TWVfL+/vf5dtc+Bp7KIKMmAwCjn47bpna5bPURRVGY/HAf/v5f3+C0ahh8dR7pmqytOsfe1cvIXLsSa61rcrYpMopB5ydn+7SOFb6FEFcnASdalMVu4R+H/sGy7evod2ISM6qeBEBnVOg/IYEBU5KuWn0kwOTDpIfT2frpUUbf371ZuybN5WXsWr6YgxvW4bDbAAiLS2DIHd+TydlCtEHyGytahN1p5/Ojn/OvbZ/R/dhIZp55GgBFB2mj4hg0o8t1Vx9JHRTdrN2SlQX57Fr2OUe2bmo8Ofuu++g2cKhMzhaijZKAEx6lairrTq5j4bYPic3ux9TyJ9GhAzRSh8QwdFYyIZEBXmlbybFcdnz5Gcd2b79ocnY/htx5L4npMjlbiLZOAk54zI7iHSzY9h4BBxMZV/Ioes31v1tS3zCG39GN8LiWr6CvaRqnD+5n55efcfrgfvf2boOHM/TOe4np1r3F2ySE8AwJONHscs7k8Kftb1O3N4Dbiu7DR3VVzI9JNTHyrlRiUkJavE2aqnJszw52fvkZJcdygYbJ2aPGMeSO7xEen9jibRJCeJYEnGg2hTWFLNj1Lvnbq+lfOAV/h+sKLTTen1F3dyehV1iLd/s5HQ6yv93MrmVfXDI5ewqDZ92NKTKqRdsjhGg5EnDilp2tP8vCzIVkfnOS/vmTibd1AiAo0sjIO3vQ9bbIZg22nO++YcOHf2bCj56gx/BRlz3GbrNycON6di9fjLm8DHBNzh4wbSa3ZdxOQEinZmuPEKJ1koATN63WXss/D3/Exo276XNyAqPqBwPgG6JjxO3d6TksBp2+eUcg1ladY/3CBVhrLaz/4G0Seqc3CitrrYXMtSvZu3qZe3J2QEgnbpt+B/2nTJfJ2UJ0IBJw4oY5VAeLjy5hyYZ19Dw6mtG1rkVIDQEwbEYqaWNiMRj113iVG6dpGus/eAdbfR0Atro6vvrLu9z+3EtYzp1l76qlZK5bha2uFnBNzh486x7Sxk+SydlCdEAScOK6aZrG16e/5u9ff0bikYGMqn4QAMVHY9CUZPpPSsTHz3P/S+V89w3Hdn13oT2qytGd2/h83n9RePiQe3J2eHwiQ+74Hj1GjJHJ2UK0Miuyinh12SFeuT2dGX07e/Rc8tsvrsvukt38ecPfCc3qzrBz97s26jX6jotnUEYy/kGeK3YMrq7Jrz54B1AArdG+U/tda7HFdOvO0Dvvo+vAITI5W4hWqKLGykuLD2Cud/Di4iyGpoQREeS53hUJOHFVR88e5d0tf0HbGUH/yrsA0BSN7sOjGDErlaBQP4+3oXHXpHbZY+J6pnH/K7+VydlCtFKapvHykgNYbK5qQRabk199eZD3HxrosXNKwInLKq4p5r1tH1D5rULP0knocN1TSxgQwpg7e9EpuuWqjxQfzW7UNXk5hdmHqCw4LYuNCtFKrcgqZu2hUvdjp6qx5mAJK7KKmNk31iPnlIATjVRZq/jLrg/J3XiG3kUjiNZcXY+RPfwZf086kYnBLdaWyoLT7FuzgkObv77qcYpOR7dBwyTchGilKmqsvLzkQJMbDArw0uIDDEsJ90hXpQScAKDeUc+/sj5hx7qj9MwfSV+nPwCmRAMT7+1LbGrLzBtTnU6O791J5prlnD6Y5d4e2jmO6spyHDbbJc9Q8PH3Z9JjT7ZI+4QQN+birslLbzBogMXqua5KCbgOzqk6WZqzjDWrdtD9xHD6OSYB4BcNE+7pQ5c+ES1yX6vWXMXBjevJXLeS6opyABRFR9dBQxkwbRYJaX3I+e4bVv7pjUueqTH58adk4rYQrVRuaU2jrslLOTVXV2VuaTXdo5u3h6hDBtz8+fNZvHgx2dnZ+Pv7M2LECH73u9/Ro0cPbzetxWiaxsZTG/l8xXoScwfSzzYFAEMnlbF3pdFjcAyKzvPBVpp3jH1rV5D97WacdjsAfsEm+k6YQr8p0zFFXCil1WP4aHK2fcPxPTvQVNXdNdlj+GiPt1MIcePsTpUdeRUYdWBXL3+MXnMyuVdEs4cbdNCA27x5M7Nnz2bw4ME4HA5efvllpkyZwuHDhwkMbP+VLvaV7uPvKxYTcbAXaXWTAVACHYyY1YM+oxPQN3P1kUs5HXZyd2wjc80KinKPuLdHJXdlwLRZ9BwxBoNP02kHiqIw+fHZ5B/Kwlprka5JIVopTdP4+kgZ81YfIa/cAoCiqWgocFGPkKKp+NutvGpdCIxo9nYomqZdftx1B1JeXk5UVBSbN29mzJgx1zzebDYTEhJCVVUVJpOpBVrYPI6fPc5fVn+Cz554oiyu6vmar4OB07oweGJXDD7NX33kYjVnz5D11WqyvlqD5dxZAHR6A92HjWTAtJl0Tu15Xd2h11OLUgjhHQcLq3h95RG+y6sEIMxfxw+PfIyh3MEbgx9qcvyLu/7BDGUH8R+vhqhe13z9G/n87ZBXcJeqqqoCICws7LL7rVYrVqvV/dhsNrdIu5pLqaWUD776iNpvg4g3u/5K0gxOeo+PYeT0Xvj6e+5/A03TKMrNZt+a5Rzd8a17xezA0DD6Tcqgz8SpBIVe/ud+JT2Gj5ZuSSFamaJzdfxhbQ6L9xUC4GPQ8eiIJO7f83/UZe5GA7bG9WV7TBqqTo9OdTK8+BBjCrOoxh9rlQHfZl7co8MHnKZpzJ07l1GjRpGenn7ZY+bPn8+rr77awi27dWabmf/b/C+KNtpJOjOAUEDVOUke3okJd/QjwOS56iN2m5Wcb7ewb80Kyk4ed2+P7dGbAdNmkjpkOHqD0WPnF0K0jBqrg/c3HeeDb/KwOlw32u7o15knzFvw+e9fUHfG9UetgsbTmYvZPykVi9GPAIeVp/YvBkUjePxYfFNTm71tHb6Lcvbs2axcuZKtW7cSHx9/2WMudwWXkJDQarsorU4rH+/4jENry+hS2hcFHRoq0QP8mPq9gZjC/T12bnN5GZnrV3Fgwzrqq11XugajDz1HjaX/1JlEJ3f12LmFEC3H4VRZtDuf/1mfS0WNa/rO4C6hzNEdJObv72CrdG3T+aiEjk/n7JbjqHX1bIntz/t97+CnWV8yumg/ukB/uq77CkN4+HWdV7oor9PTTz/NsmXL2LJlyxXDDcDX1xdf39Zfjd6pOll2YBVbl2eTVNiPZM1VHcDUE6bfN4zw2CCPnFfTNE4f3E/m2hUc370TTXP9FWeKjKLf5On0mTAF/+DW94eAEOLGaZrGppxy5q06wtGyGgC6hAcwJ7iA9H/9Cnt5PTZcwRY+qTehv3wTfUwyfitXUfjcc4wp2s+Yov0Nr6bQ+b/nXXe43agOGXCapvH000+zZMkSNm3aRHJysrebdEs0TWPTsW9Y9eUO4k6kk6K6Jkz6JtmZ8cAQOieHeuS8tvo6Dm/ewL61KzhTmO/enpjejwHTZpEycDA6nWcHrgghWs7hIjPzVh1h67EKAEIDjPwk/BzjP3sVtaQaO6AzqoSP60ro839EH39h0Ejw9AyCVyyletNm0BR316QpI8Nj7e2QATd79mw+/vhjli5dSnBwMCUlJQCEhITg7++57jtPyCzMYtEX64nM6UEXpyvYlGgr0+6/jZTe0R4555miQjLXreDQpq/da68Zff3oPXYiA6bOJDw+wSPnFUJ4R6m5nj+szeHzvQVoGvjoFR6MsnLPst9iLKxAxRVsYaPiCfvFG+hTmlYlURSFmP+eh2XyJNTaOnQB/sT8v3kebXeHvAd3paHoH374IT/84Q+v+fzWME3gxJmT/P2LpQRkJRJoDwFADa1j/D3ppA1MbPbqI5qqciJzD/vWLOfk/r3u7aGdY+k/dSZpYyfKatlCtDMWq4M/b8njgy151Nldg0WmRTh5+Kt3CTt5CmgItmGRhP18Hvoe1zHNavVqSl6fR8yvXsY0bdoNt+lGPn87ZMDdKm8GXJmlnL99+QXOneGYrK5+a0dgHcNu78rg0T3QNXP1kfqaGg5ucpXQqip1XemiKKQMGMSAqTNJ6jtA1l4Top1xqhqf78nnj+tyKat2DbDrb9J4bPvf6Zp7EGgItkEmwn72Kvq+01usbTLIpB2qtlbz91VfcO4bH0JruwPg8K0nfWoM46aMQ29o3pApP32SzDUrOLx1I46GEaS+gYGkj59C/8nT6RTj2ZV4hRDesSXXNYAku6QagHg/ePTwlwzN2opCQ7AN8CPsmZfRD7y3UWWS1kYCrpWzO+386+svOfVVLRHmREIBh9FKlzEmpt8+FqNv8w3iUJ1Oju36jn1rV1Bw+KB7e0RiFwZMm0mvkeMw+nl+gVMhRMvLKalm3qojbM51FTs3GeAHJzcybe8afFSnK9j66Al78pfoR/wQ2sAAMgm4VkrVVJZuW8v+lUVEnkkmgnCcOjuRQw3cec9E/IOab5J2bdU5sr5ey/6vVlNT6Rodpeh0pA4ezoBps4jrlSYrZQvRTpVV1/M/63NZtCsfVQODAneW7eG+nV8SbK9DZ1AJ66sR9vhs9GOfAEPrnzJ1ngScF3y+eh0nV9eTPN2Pe6ZNabJ/Q9ZWtiw+QmRJVyJJRlWcBPa1c88DYwkJbb6BHCXHctm3Zjk5332D0+EAwN8UQt+J0+g3OYPg8IhmO5cQonWpszn54Js83t98nFqbawDJmKqjPLLzc2ItlegMKqF9HYT/6EfoJ8wBX8/Mo/UkCbgWVlhWQv4KO37OQE6tqKfwthLiomIA2HM8i5Wf7iT0VBci6YqGir67he89OJrozjdWr/FKHHY7udu3sm/NckqO5bq3x3TrzoCpM+k+fDQGo5TQEqK9UlWNL/YW8Id1OZSaXffXe9WW8Ojuz0k7c9IVbOlWwn5wH4apL0BA83z2eIMEXAtSVZV//vkrAp3RKCgYHT58tPAr7nl0JJ/9eyOBufGEaykAOBLPcvsDw0lOiW2Wc1dXVrgq+X+9ltqqcwDoDQZ6DB9N/2kz6dyt46yFJ0RHte1YBf+98giHi11l9GJsZn64fyljCvejN6iEptURdu9MDNN/Babm+ezxJgm4FrR4zVcEF174n0aHnqCCWFa+loNJ6wJAffQZJt/bj77pE275fJqmUXjkEPvWruDozm1oqquEVlBYOP0mT6fvxKmyErYQHcCxsmrmrcpmQ3YZAIFOKw8cWc/teVvx1dkI62Uh7M4JGGb+BsLbT71YCbgWUlhWQv5KO0Z0KDQe0m/QfLCYzjDynm6MGDL+lgd02K31HNm6icw1Kyg/fdK9Pb5XOgOmzaTroGHoDfKfXoj2rqLGyltf5fLJznycqoZeU5mR9y3fz1lPJ7WGsB4WwqYPwTDrVYjp4+3mNjv5lGsBjbsmm85XU1HRgusZObT/LZ3nXGkJmetWcnDjOqwW1yq6Bh9feo0ex4CpM4lMats1N4UQ16fe7uSvW0/w3qbj1FhdA8iGFx3gx4dWkmAtIyzVQtjE3q5gSxru5dZ6jgRcC8jMOdKoa/JSOnQEFcaSmX2Y/j1739Bra6rKqQOZ7FuznLx9u6GhME1IVDT9p8wgffwU/ILa3ugnIcSNU1WNpfsL+f2aHIqq6gFIPZvPYweX0+/cMVewjUnEMGsBdJvUqidpNwcJuBbQv0cv1sXtI7AwGh1NJ0eqOKmNL6V/z+u/72atreXQ5q/JXLuCs8WF7u1d+t1G/6kzSR4wUCr5C9GBbM+r5PWVhzlQ6BpAEll7lh8eXsX44r2Ep1oInxWOYcbrkHY3dJDyehJwLUCn0/HwTybx6at7MDp9G3VTaqjYDTYe+s9J1/ValYX5ZK5dwaHNG7DX1wHg4+9P2rhJ9J8yg7DYK69rJ4Rof46X1/Db1dmsP1wKgL+9ngdyv+bOk1uI6VpF+IOBGKa9BgMeAn3HmgIkAddC4qJiSJhhpHRZ47+cFHQkzjC658Jdjqo6ydu7m31rlnP6QKZ7e1hcAgOmzqT3mPH4+Ad4rO1CiNbnjMXGW1/l8vH2Uzg00KlOpp/czkNH15KcWEb4PXoMk38BQx4HY9taBqy5SMC1oLunTeKNPR/jd6IKZ+1mDAHjqE8xMTvjocseX1dt5uDG9WSuW4W53PXXmaLoSBk4hAHTZpKY3k9KaAnRwdTbnXz47Qne+SqHGtf4EYYWH+LR7OX0izlJ+AwVw4QnYcRT4Bfi3cZ6mQRcC9LpdNz7/SF8+fILoNlw1H7F9x78bZPjyk7msW/NCrK3bsJhtwHgFxRMnwlT6Dd5OiFRnlnIVAjRemmaxvL9Rfx26X6K6lyDybqeK+Dxw8sZH7qf8IlWDKN/DKOfg6BIL7e2dZCAa0GapnFwyWeAvWGLnUNffk7X517C6XBwdOc2MteuoDD7sPs5kV1SGDBtJj1HjsXo03aKnAohms/OE5X8v0W7OHDOVTMyvO4cP8pexT2+3xA5woJh2AMw7nnolOjllrYuEnAtKOe7bzi267sLGzSNozu3sfx/fkthzmEsZ88AoNPrSR06kgFTZxLbo5d0QwrRQZ0or+H1j7fxVbHrj2I/h5X7jm7gx6wirv9ZjLfNgAn/BZFSau9yJOBaSG3VOb764B1AARovop67fSsAgZ1C6TtpGn0nTiMoLLzlGymEaBXOWqy8+fFWPjlmwaHo0WkqU0/t4CnrErr3LMKYNgYm/hriBnq7qa2aBFwL0DSN9R+8g62+jkvD7bzorqk8+Nob6A0daxivEOICq8PJX/79De9lnaVG5wOKnsGlR/hZ9acM7noUY7cBMPFdSBnr7aa2CRJwLaAy/1TjrsnLKD1+lLPFRUQkJLVQq4QQrYWmaSxZspU/bCuiyBAEOh+Sq4qYc/YzpifuwjioO0z4J/Sc0e6rjzQnCbgWEJ6QRLfBwzm+Z4e7ov/FFJ2OboOGSbgJ0QF9u/pb5q0/xiGfCDAEEVZfxU8rl/Jw5/X4dY+D8e9C3/tAKhPdMAm4FqAoCpMfn03+oSystbU07qZU8PH3Z9JjT3qreUIIL8jdtJ35SzLZ6J8APhH4Omw8XLmOpyKX0KlvEIyZDwN/CAYZPX2zJOBaSEBIJyY9PpuVf3rjkj0akx9/StZlE6KDKN62kz99spXP/bri8E9A0VRmnPuOF0yfEN/dASOfg6E/BV8pkn6rJOBaUI/ho8nZ9o27q/J812SP4aO93TQhhIed27Wbv364lr/5pVId6BrWP6TmMP/l/xF9Ekpg6E9g5BwICPNyS9sPCbgW1Lir0iJdk0J0AJY9e1jywRLeM3Sj0ORa8zGlvoiXjR8xIfIgysD/gDG/BFNnL7e0/ZGAa2EBIZ2Y/J9PseHDPzPhR09I16QQ7VTt3n188/6/eFtN4mCEa1HRMHsVP9N/wYMhGzH0vRvG/R+Ed/VyS9svCTgv6DF8tHRLCtFGmVevpuT1ecT86mVM06Y12V+7bx8H3/0/3q2NYlPCeAB8VRuPsoong5YR1GMcTNgCMekt3PKORwJOCCGuk6Oykn8vWMR7g57kybf/zY8GD8YQ7qo6VLtvHyffXciHlQF82XUi9nAjiqZyp/Ytv/RfROeknjBpKSQO8/K76DgUTdMuX1pDXJHZbCYkJISqqipMJtONP3/hq5S89wkxT34f0+O/9kAL27cVK7/k1a11vDLanxnT7/R2c0QHoWkaB5/5OQ/4DMdi8CPIXscn9h10e/Qhihe8yxeFTv7ZcwrmhtGPw9TD/Mrvn6THmmDib6DbJJmk3Qxu5PNXruBamON0LsVvf4Jqh+K3PyZg6gMYErt7u1ltRkVpES9utlOtBPPCpjqGDiwiIjrW280SHYB51SrmV0VQF+MDikKtwZf55eFMnvP/+GvaTAr6RQGQohXyss/HTIgwo0z8DfS+C3S6a7y68AS5grsJN3sFp6kqhXePZGV1Iu/3uZOfHljCdFM+8UuuXsZLuGiqyk9ef5+vquNRdXp0qpPJwQX8+b9kJKrwLEdlJX/9j58zv8/3rnhMmGbmZ8bPeaBTNsbxv4D+PwC91JZtbjfy+St/VrSg6g9eIz/Pwf/2u5ezvsH8b797yT/uwLzwVW83rU1YsXIp6yxJqA0li1SdnrWWJFas/NLLLRPtiaZp2IuKqNm8mcq//pXCXz7Pnln38L89ZqBoTUvtoWl8r3Qjm0Jf5eGMsRif3e2qQCLh5nXSRdlCHKdzKVrwb94e8Ah1hgtdHAv63cV//ekfOIuOog8xoej1oNc3fDeg6HRgMKDo9CgGvasencGAotej6A2uYw1G0OtQ9MaGxwbXcw2XPNYbwGB0fVd0oOhd9wR0+ose6y56rLvk8cX7lcscf/5x8/zd5FQ17E4Vm1OltLiIF75xgqI1uo+haCovbrYzbJB0VYobo2kajrJyrMeOYj16FOuxY1iPHsWcd5pCxY+iwAgKgyIpCIpgx9CfYjH6XfYemk7TKHaE4zvzE0gf4IV3Iq5EAq4FaKpKyTOPsCmqP9ti+7q3qzo938b1Y3Nhf8b8e0/LtAVQFQW7Xo9Db8Ch1zV81+PU6bDrDTh1eux6PU6d6xi7TodTb8Cu0+No+LLrDA3/brzd/Vg5v92AXTFceI5iwK4zYFcajlH0rv2KvvG/0aMqlwSlzr/p+1F0VOPPrD9t4t5+4SREhpAYE0VCTDTRnQLQ6+SmvgDHmTNYjx5rCLKjVB/N41TRGQo0XwqCIikKjKAoKJLC6F5UJN/Y3FRVp+PbuL6ciuyG3E1vXSTgWoB1xzry8xy8Pel7KJqKdvEHt6bx5m33ccYvmIAgBafRBzt67Oiwo8eh6Fz/bggAB7qGEGjY5g6LhuBRXKHiCpTz2xuHkHZpcLR1ikKxGsL/7nMAlQ1fRzDiIE5vJtG/jsRABwkmPYmh/iREmkiIiSIkPAaCosEn0MtvQDQXp9nsuhLLycVy5CCn8go5UWnltC6o4YosgqLArpTHD0RNuPLvQQg1dFFKSFGKSVJK+Np5G4e05KZ/dIHrXrBuD92VFKCXB9+duFEScC3AZ8hk3ht1iDqDT9NwURSsBl/+3Nd7w92NOgWjXsFH5/q3oeG7UQGjjsbfFQ0DGkZFwwcNIyoGVIwNXwZNxag5G/7txKipGDQHRs2JQXW69ql2DKoTg9qw3el6bFTtGJyOhi87RqcDvWpHMZv5tX4QO2J6u++/XUzRVBLNpfQ+c5KSwHBKAsIoCwjFrjNw0hnGyRqgBii9+FlnCdHySdCVk6g/Q4KfhYQAJ4kmHYmhfsSGm/AxRbsCMCjK9T0wEvTyK+NVmgb153CWnsB2aB+12dmcPlnO8QonJ+oDKDCEUhgYQVFQBCUBQ1Dj9RB/+ZcK1OpIVkrooi8lxc9ClyAHXTrpSQ4PIDQsAoJjIbg/BMXw0Jp5jD/YmRr8Gv0OK5pKIPW8nl4IURJurY38traAo+W1bDH1vOZxw1PCCQv0cYWNQYdR7/py/Vu58Fjf8Nhw8WOd+3k+ep17n1GvXNhvuORxw36llc/N0TSNl2Y/wwOObtQafZt+wNjreatqC13umoG9sADb6b3U556iqLKaIs3fHXolAWEUB4ZTGhDGOb9gqpQgqrQgDjqSXQFYA5S5XlenqcQoZ0jUZZKglJOolJGglJPgbyPRpCPCFIgSfD78Yi6E4PlA9AtpvXOeDi6G1c/D9Dcg7S5vt+YCmwWqS6C62P1drSigLu8EBfk1HKs0klcfwimiyPeNoigoguKAUTgCDXCFi3A/zUqS7hzJQXWkhGh0CfUhOSKILp0jiIhMQDGNdxU3vsZ/q4i7/8C8E//J03WPNdquKTrmB35CxJ0Lm+unIJqRTBO4CTc6TUDTNJ74aA9fHSrBSdNfJD0ak9M78/5DAz3R3HbBUVnJXx+aw/x+DzTZ91LWv/nxP99yV5S4mNNsxl5QgC2/AHtBPrb8fOwFhZgLS8g/V0+Jr8kdesUNQVgaEIbV4HPV9vhrVhKUMhJ1ZcS7A7DMHYQBRqVp6AVdHIjn/x3Vsut91ZTDgoFQX+UK4af2QFCkZ8/psEFNaUNoFTUOMbPrsXq2hIJKA8dq4zhmi+WkFsNpfTQFflEUBUZgu8qIRB/NQbzOQpdglZSYILrGhdMlNorkuDiiQwOb7Q847cAX/ORfB/hKve3CNBXdHv78UD9Iv7tZziGu7UY+fyXgbsLNzIOrqLEy4Q+bqK6zozUaBagR7G9kw8/HEREkCxteTdWKlfz00yy2x6S5P2CGFx/ive/3x5SRccOvpzmdOEpLsRUUYM8vwFaQ7/qen09p2TmK6qE4MOzCFWBDEJb7h1zzPmY4VSS6A8/1PV4pJ1FXRmcq0SsX/dr5dXIFXnD01QPRP/TWRqhqGix6mBWHKnjV/jCv+PyTGWmRcP8/b+71VBVqK9whdeHK66LH5mLXMQ2nr1SDya2N53hdLHmOzpwkhnxjFIX+EdQZ/K54Kr3mJI46kgJ1JMeE0rVbHF0TIukSEUDnEP+WGUykaVR89BjjD0yjWvEnWKtlY991RDz0F8+fW7hJwHnYzU70Xr6/iKc/2ddk+4LvD2BmXxnifi2apnFw9jM84DcWi7GhVJJ1C33eedsj51Nra13hV1CAPT8fW0Eh9vx8LIWFFFZYKDYEXRR+F4Kwxifgqq9rxEms7iyJlJCglLi7QM+HYQiWy/eY6QwQGHUh/K4YiFcYOHPwCyothOMhAAAgAElEQVQ+m8P4uj9SrQS4PqD9nyPi3j81vgJpuM/VKKQu6jZ0/7umFFRHk9NUaYHkqTEcq4vluC2WE84YTuliyPeNwmJoOhLW/fY0lRiHhQQfJ106+dE1Poyu3RPomhxDfGgABn0rGBxVU86KNx/n1dp7eSXgM2bM/cDzV8CiEQk4D7vpSiaXdFVK1+SNc1RW8n8PzeG9HtN5MmclP/roT5ftmvQ0TdNwVlQ07vrMd4VhZXE5hRYnJQGhlAQ0DsDSgFAcuqvf+g7W20nwsZBoaAhBZz4J9jwSlTLilAp8laah0oRPcOPQ8zWhZS3iJ5Ynm3axBb4H3SZC3bkL4eWou+rLV2v+nNRiOKHGcJxu5DniOOmM4DShVOmvHGKKphJZbyZeqyUpQEeXyEC6JkWT2juZLqkJ+BqbDiJqdVrrPcwOQgLOw26l2PL5rkpzvQOTn0G6Jm/CtZYraQ1UqxV7YZFr0Is7/PKpyy+kpOwcxYo/JYFhFAeEUxp4YQDMWb9r/MKiEe2Pa9qDr4VE/VkSlFISnadJtB0n0nIMxXn5cFruGMbTjmeabF9g/BMz9TuabK/1i+akXy9OGlI4QTx59aGcsPhzqt6PSu3q9yjD66qIra0kQW+ni8lISudOdO0WS9c+qQQnxrsKGAhxEyTgPOxWVxNYkVXEq8sO8crt6czoK6v4djSapuE8d+5C12fDlZ+tIJ/qwhIKqqyU+nVy3f9rCMDihnuA9dcYkOKrV0gI9SPBpCMxwBWCCfYTmI58wuPWn2NRmg5zD9CsvOLzN86k3sdJYsk7p3HyrJ3S+qt/NHSqrybWUkFcbSWJPipdwvzpmhhBSo9Ewnp2x5iQ4KrII0QzkoDzsFsNOCGuRrPbsZeUNIz+zHcPgLEVFFJRUkmhw3Ch2zMg3P293D/ksvMEb0WwzUJsTQVxlgpiLZUk+UNyVDBdk2MI79EV39RUfJKSUIxSd1G0DFkuR4g2TDEa8UlIwCchgcDhwxvtSwGc1dUXwq+gsOEe4GHqjhZSdKaGYt+QiwIwjFPBMZwKuXZPwaCSI6SeKyDOUkFSgEJKXBiR3brgm9oN39QJ+CQno/OV7nTRdkjACdHG6IOD0ffqhV+vppUzuqsqjrKyi7o+86k9vJsXi5Lc0ysudX66xVsjwwga+Ri+XVPQBVx9JKgQbYEEnBDtiKLTYYyJwRgTQ8DgwYDrnt+Lz/ycB5w2apWmlWACHFZe6FRJ5BMveqvZQnhEhx7K9O6775KcnIyfnx8DBw7km2++8XaThGh2iqLQ65WXeCZ7ZZMJ6pqi45ncVfR6RcJNtD8dNuAWLVrEnDlzePnll9m3bx+jR48mIyOD06dPe7tpQjQ7Q3g4Dzz5PUYUZaFTnYCra3JkYRYPzL7PK3MJhfC0Dhtwb775Jo8++iiPPfYYvXr14q233iIhIYH33nvP200TwiNM06fzYkgF/k4baJq7a/JmypwJ0RZ0yICz2Wzs2bOHKVOmNNo+ZcoUtm3b1uR4q9WK2Wxu9CVEW3O+q/LZ7BWEWqt5NmeldE2Kdq1DBlxFRQVOp5Po6OhG26OjoykpKWly/Pz58wkJCXF/JSQktFRThWhWhvBwHnjqfhbtfpf7n35AuiZFu9YhA+68S5fR0DTtsktrvPjii1RVVbm/8vPzW6qJQjQ7U0YG3bd+02rLnAnRXDrkNIGIiAj0en2Tq7WysrImV3UAvr6++MoEVyGEaFM65BWcj48PAwcOZP369Y22r1+/nhEjRnipVUIIIZpTh7yCA5g7dy4PP/wwgwYNYvjw4SxcuJDTp0/zxBNPXPO558t3ymATIYRoWec/d6+njHKHDbj777+fyspKXnvtNYqLi0lPT2fVqlUkJSVd87nV1dUAMthECCG8pLq6mpCQkKseI6sJ3ARVVSkqKiI4OPiyg1KuxWw2k5CQQH5+vqxGcBPk53dr5Od3a+Tnd2tu9eenaRrV1dXExsaiu8a6gh32Cu5W6HQ64uPjb/l1TCaT/ILcAvn53Rr5+d0a+fndmlv5+V3ryu28DjnIRAghRPsnASeEEKJd0r/yyiuveLsRHZFer2fcuHEYDNJLfDPk53dr5Od3a+Tnd2ta6ucng0yEEEK0S9JFKYQQol2SgBNCCNEuScAJIYRolyTghBBCtEsScEIIIdolCbgWNH/+fAYPHkxwcDBRUVHceeed5OTkeLtZbdb8+fNRFIU5c+Z4uyltRmFhIQ899BDh4eEEBATQv39/9uzZ4+1mtQkOh4Nf/epXJCcn4+/vT0pKCq+99hqqqnq7aa3Sli1bmDVrFrGxsSiKwpdfftlov6ZpvPLKK8TGxuLv78+4ceM4dOhQs7ZBAq4Fbd68mdmzZ7N9+3bWr1+Pw+FgypQpWCwWbzetzdm1axcLFy6kb9++3m5Km3H27FlGjhyJ0Whk9erVHD58mD/+8Y906tTJ201rE373u9/x/vvvs2DBAo4cOcIbb7zB73//e95++21vN61Vslgs9OvXjwULFlx2/xtvvMGbb77JggUL2LVrFzExMUyePNldzL5ZaMJrysrKNEDbvHmzt5vSplRXV2upqana+vXrtbFjx2rPPvust5vUJjz//PPaqFGjvN2MNmvGjBnaj3/840bb7r77bu2hhx7yUovaDkBbsmSJ+7GqqlpMTIz229/+1r2tvr5eCwkJ0d5///1mO69cwXlRVVUVAGFhYV5uSdsye/ZsZsyYwaRJk7zdlDZl2bJlDBo0iHvvvZeoqCgGDBjABx984O1mtRmjRo3i66+/Jjc3F4D9+/ezdetWpk+f7uWWtT0nTpygpKSEKVOmuLf5+voyduxYtm3b1mznkTozXqJpGnPnzmXUqFGkp6d7uzltxr///W/27t3Lrl27vN2UNicvL4/33nuPuXPn8tJLL7Fz506eeeYZfH19+Y//+A9vN6/Ve/7556mqqqJnz57o9XqcTievv/46Dz74oLeb1uaUlJQAEB0d3Wh7dHQ0p06darbzSMB5yVNPPUVWVhZbt271dlPajPz8fJ599lnWrVuHn5+ft5vT5qiqyqBBg5g3bx4AAwYM4NChQ7z33nsScNdh0aJFfPTRR3z88cekpaWRmZnJnDlziI2N5ZFHHvF289qkS9fT1DTtptbYvBIJOC94+umnWbZsGVu2bGmWdeU6ij179lBWVsbAgQPd25xOJ1u2bGHBggVYrVb0er0XW9i6de7cmd69ezfa1qtXL7744gsvtaht+cUvfsELL7zAAw88AECfPn04deoU8+fPl4C7QTExMYDrSq5z587u7WVlZU2u6m6F3INrQZqm8dRTT7F48WI2bNhAcnKyt5vUpkycOJEDBw6QmZnp/ho0aBA/+MEPyMzMlHC7hpEjRzaZlpKbm0tSUpKXWtS21NbWNllBWq/XyzSBm5CcnExMTAzr1693b7PZbGzevJkRI0Y023nkCq4FzZ49m48//pilS5cSHBzs7ocOCQnB39/fy61r/YKDg5vcrwwMDCQ8PFzuY16Hn/3sZ4wYMYJ58+Zx3333sXPnThYuXMjChQu93bQ2YdasWbz++uskJiaSlpbGvn37ePPNN/nxj3/s7aa1SjU1NRw7dsz9+MSJE2RmZhIWFkZiYiJz5sxh3rx5pKamkpqayrx58wgICOD73/9+8zWi2cZjimsCLvv14YcfertpbZZME7gxy5cv19LT0zVfX1+tZ8+e2sKFC73dpDbDbDZrzz77rJaYmKj5+flpKSkp2ssvv6xZrVZvN61V2rhx42U/7x555BFN01xTBX7zm99oMTExmq+vrzZmzBjtwIEDzdoGWQ9OCCFEuyT34IQQQrRLEnBCCCHaJQk4IYQQ7ZIEnBBCiHZJAk4IIUS7JAEnhBCiXZKAE0II0S5JwAkhhGiXJOCEEEK0SxJwQggh2iUJOCGEEO2SBJwQQoh2SQJOCCFEuyQBJ4QQol2SgBNCCNEuScAJIYRolyTghBBCtEsGbzegLVJVlaKiIoKDg1EUxdvNEUKIDkPTNKqrq4mNjUWnu/o1mgTcTSgqKiIhIcHbzRBCiA4rPz+f+Pj4qx4jAXcTgoODAdcP2GQyebk1QgjRcZjNZhISEtyfw1cjAXcTzndLmkwmCTghhPCC67k9JINMhBBCtEsScEIIIVrM0d2lfPjLrRzbU+bxc0nACSGEaBG1Zhub/pXT8D2bWrPNo+eTgLsB77zzDr1792bw4MHebooQQrQpmqax+eNs7FYHALZ6B5s/yfHoORVN0zSPnqEdMpvNhISEUFVVJYNMhBDiOhzdXcq6vxxqsn3KY2mkDoq+7te5kc9fuYITQgjhUee7Ji9n88c5HuuqlIATQgjhMZd2TV7Kk12VEnBCCCE85kyRhbzMCjT18vs1FfL2lVNZVNPs55aAE0II4RGaqlFZVIPB98pRo+ggZUAk4bFBzX5+qWQihBCiWWmaxqkDlWxflkdlwdWvzHz8DIx9sIdH2iEBJ4QQotkU5Jxlx9LjlOSZATD66ek/KZHgMF82/CO7yfFjv9+DAJOPR9oiASeEEOKWlZyoYsfSPAqyzwJgMOroMz6e26Yk4RdkRNM0TmZVcCLLdT9O0UFyv8gbmiJwoyTghBBC3LSKghp2LMvjZFYFADq9QtroOAZmJBEY4us+TlEUxn6/JwU527HVOTzaNXmeBJwQQogbdq60lp3L8zi621VTUlGgx/DODJ7eBVOE/2WfE2DyYdwPerD106OMvr+7x7omz5OAE0IIcd3MlXXsXnmS7O0laKqrEFa3QVEMmZlMaEzgNZ+fOijao92SF5OAE0IIcU2WKit7Vp/i0NZCVIcr2Lr0jWDo7clExF978VFvkIATQghxRfU1dvatP0XWhgIcdtds7bgeoQy7I4WYlBAvt+7qJOCEEEI0YatzsH9DPpnrT2OrdwIQnWxi2B0pxPcM83Lrro8EnBBCCDeHzcmBTYXsXXuKeosdgPC4IIbdkUJSn3AURfFyC6+fBJwQQgicDpUj3xaxa9VJaqtc1f07RQcwZFYy3W6LQtG1nWA7TwJOCCE6MFXVyN1Rws4VJ6iurAcgOMyPwTO70GNoDDp985YsXpFVxKvLDvHK7enM6Nu5WV/7UhJwQgjRAWmqxvF95excnsfZklrANU9tYEYX0kbFojc2fy3+ihorLy0+gLnewYuLsxiaEkZEkO+1n3iTJOCEEKID0TSNUwcr2bEsj4p8VyFk30ADt01Jos/4eIw+eo+d9+UlB7DYXANWLDYnv/ryIO8/NNAj5wMJOCGE6DAKc86yfWkeJXlVQEMh5IkJ9JuUiK+/Z+NgRVYxaw+Vuh87VY01B0tYkVXEzL6xHjmnBJwQQrRzpSfMbF963F0IWW/U0XdcPAOmJuIf5NlyWeDqmnx5yQEUQLtouwK8tPgAw1LCPdJVKQEnhBDt1OUKIfceFcugjC4EdvLcva+L1dkc/Oc/dlNd72gUbuAKO4vVc12VEnBCCNHOnCutZeeKExzdXQpaQyHkYTEMnpF8xULIzanwXB0bssvYmF3G1mPl2ByXRtsFTs3VVZlbWk336OYt+SUBJ4QQ7UT1mXp2rTxB9ncXCiF3vS2KobdfXyHkm+Vwquw5dZYNOWVsyi4np7S60X5fgw6bQ21yBQegVxQmp0U3e7hBBw64FStW8Nxzz6GqKs8//zyPPfaYt5skhBA3xVJlZc+aUxz65kIh5KQ+4QydlUJkomcKIVfWWNmcW86G7DK25JZjrne49+kUGJgUyrgeUUzoGUVEkA8T/7i5STelAgT66vnvO9M90sYOGXAOh4O5c+eyceNGTCYTt912G3fffTdhYW2jvpoQQgDUW+zsW3earI35OGwNhZC7d2LoHV3p3LV5CyFrmsahIjMbssvYkF3G/oJzaBelVWiAkbHdIxnfM4qx3SPpFNB48Mrrd/Xh6U/2NX5NYN7dfTw2F65DBtzOnTtJS0sjLi4OgOnTp7N27VoefPBBL7dMCCGuzVbvYP/XjQshR3U5Xwg5tNnqRdZYHWw9WsHG7DI25pRRVm1ttL93ZxMTekYxvmck/RNC0V+lnNfMvp1ZkVXEV0fKcKoaep3C5N7RHpsiAG004LZs2cLvf/979uzZQ3FxMUuWLOHOO+9sdMy7777L73//e4qLi0lLS+Ott95i9OjRABQVFbnDDSA+Pp7CwsIWfQ9CCHGjHDYnBzY3FEKuOV8IOZCht6fQpW9EswRbXnmNa4BIThk7T5zB7rxwmRbgo2dktwhXqPWIIibE77pfV1EUXr+rD98d34S53kGgj+e6Js9rkwFnsVjo168fP/rRj7jnnnua7F+0aBFz5szh3XffZeTIkfz5z38mIyODw4cPk5iYiKY1vdV5tf8xrFYrVuuFv1zMZnPzvBEhhLgOTofKkW3F7F55AktDIeSQKH+Gzkqh28BbK4RsdTjZkXeGjTmuUY8nK2sb7e8SHsD4nq57aUOSw/A13Hylk4ggX+bd3cddi9KTZbqgjQZcRkYGGRkZV9z/5ptv8uijj7oHjrz11lusXbuW9957j/nz5xMXF9foiq2goIChQ4de8fXmz5/Pq6++2nxvQAghroOqauTuLGHXihOYK1yFkIPCfBk8I5mew26+EHJJVT0bc1z30r49VkFtQ/ksAKNeYUhyGOMbBoikRAY1y3s5b2bfWI92S16sTQbc1dhsNvbs2cMLL7zQaPuUKVPYtm0bAEOGDOHgwYMUFhZiMplYtWoVv/71r6/4mi+++CJz5851PzabzSQkJHjmDQghOrzLFUL2N/kwKCOJtFFxN1wI2alqZOafbRggUs6R4sa9UFHBvozvEcX4nlGMSo0gyLd9REP7eBcXqaiowOl0Eh0d3Wh7dHQ0JSUlABgMBv74xz8yfvx4VFXll7/8JeHh4Vd8TV9fX3x9W2bWvxCi49I0jdOHzrBjWR7lp11zyXwDDNw2NYk+4+Ix+l5/9+C5Whubc8vZmF3G5txyztba3fsUBfrFd2JCQ9dj784mdC213tvBxbD6eZj+BqTd5dFTtbuAO+/Se2qapjXadvvtt3P77be3dLOEEOKyCnPPsmNpHsXHGwoh++rpNymB/tdZCFnTNLJLqt0VRPaePot60XADk5+BMd0jmdAwjD/cw/e/LqumHFbMgfoqWP4sJI2CoEiPna7dBVxERAR6vd59tXZeWVlZk6s6IYTwttKTZnYsPU7+kQuFkPuMjeO2qUn4B1+9EHKtzcG2Y5UNFUTKKKqqb7S/R3Qw43pGMqFHFAOTQjE08+KlN0TTYMXPwOpaogdrDaycC/f/02OnbHcB5+Pjw8CBA1m/fj133XXh8nf9+vXccccdt/Ta77zzDu+88w5Op/PaBwshxFVUFroKIZ/Y31AIWecqhDwwowtBoVe+ujpdWcuG7FI25JSzPa8Sm0N17/Mz6hjRNYLxPaMY3yOS+NAAj7+P63ZoMWQvx3zaj5K9EcTcVoVJW+bqsky/2yOnbJMBV1NTw7Fjx9yPT5w4QWZmJmFhYSQmJjJ37lwefvhhBg0axPDhw1m4cCGnT5/miSeeuKXzzp49m9mzZ2M2mwkJad4qAUKIjuFcWS07lzcuhNx9qKsQckhk00LINofK7pNn3HPTjpdbGu2PD/V3z0sb3jUcP6NnFiy9JTXlsOJnOOr1FO/qhGpXKN7ViYCocgwr5kCX0R7pqmyTAbd7927Gjx/vfnx+hOMjjzzC3/72N+6//34qKyt57bXXKC4uJj09nVWrVpGUlOStJgshOrjqM/XsXnmCI40KIUcyZFYKYZ0bF0Iuq65nU45rgMg3RyuosV6o86jXKQxKCnUPEOkWFdRslUs8oqFrUquvoWS3CdWhAAqqA0p2m4gfbfZYV6WiXW7Ws7iq81dwVVVVmEwmbzdHCNGK1Zpt7Fl9koMXF0JOD2fo7RcKIauqRlZhFRuyy9iUU0ZWQVWj1wgP9GFcD1dJrNGpkYT4G1v8fdwUmwV2LISvX8F82o/CbU3r/caNOIMpsR6e3A5Rva75kjfy+dsmr+CEEKK1q7fY2bf+NFkbLhRCjk3txLA7UujcrRNVdXZWZhWzIbuMzbllVNTYGj2/T1yIu4JI37iQlhvGf6ucDjixCbI+hSMrwG7BUa+jeFcnXOWVL34fmqurcuQADNcRbjdKAu4GyCATIcRflhzhf7afYO7wFB69s2eT/bZ6B1kb8tm3Ph9bnatrMSopmKG3p1AfbmRZTjkbvj7CnlNncVw0jj/I18DoVNcAkXE9IokKvv46j16naVC0zxVqB78AS9mFXZ26UPxtOKq9iMbhBu6uyj0hxD/a/M2SLsqbIF2UQnRM+cU1THprM1bAD1g/ZywJnV2lrBx2JwcbCiHXVbsmVQd3DsAwOJwjNisbc8ooOFvX6PVSIgOZ0FASa1CXMHwMXhzGfzPOnIADn0HWIqi8MPCPgHC0nndicaRx9ut91Hz99TVfKmX5MnxTU695nHRRCiFEM1NVlac/2IkNQAGrBs98sJPPXx7HkW+L2b3qJJZzVsyKSlGYnrIoI/srz1K/pdL9Gj4GHcNSwpnQw7VuWlK451bZ9hhLpWvI/4HPIH/Hhe0Gf7TUDCz0x5xVSs3rG3BWrb726+n1BE+YcF3hdqMk4IQQ4jr8dWkOmbV17l42TYF9tXX84sWN+FhU8owqJzuplKGCCjTUmugc4tcwLy2Kkd3CCfBpgx+79jrIWQVZn8Gx9aA2jOpUdGhJY6jVD8GcbaH6fzbiPLfd/TR9eDimqVMIHDGSohdfRK2podEqqYqCLjCQmFd+45Fmt8GftBBCtKz84hr+sD3P9aDxGAkWK/UQfGGTToHbEkPdA0R6xgS37mH8V6I64cQW15Xa4WVgq3bv0qL7Uus3CvMxleqFW3Ge+ci9Tx8WRvCUyZimZRAweBCK3jUvr7PtFQrnPtf4HJpG51dfwXCVWsC3QgJOCCGu4tKuyUYaHuuBWf1jGd8zijGpkYQGXr3EVqulaVBywHVP7cDnUHOh5KFmSqAuYDzm0z6Y/7UTZ8WX7n36Tp0InjIFU8Y0AgYPRjE0jZbgjAyCV6+mesNGcDrdXZOmqyx9dqsk4G6AjKIUomNxqhoLFh9u1DV52eOA76fFMqRPG613e+50w2CRT6E8271Z8+1EXdB4zAXBVH+9H0f5V+59upAQgidPwjQtg8ChQ1CMV5+bpygKMa+8gmX7DtTqao92TbrPKaMob5yMohSifcsurOKva46y9lg5Zk296rGKBv0D/Vny6wkt1LpmUncWDn3pCrXT29ybNZ0vdQGjMJdEUL0rB0fphSH/OpOJ4EmTMGVMI3DYsGuG2uWYV6+m5PV5xPzqZUzTpt3482UUpRBC3JgzFhuffXuKRTtOk2e5UJXfT4N+gQHstdRihyb34HyB/318SAu39ibZ6+HoWleoHV0HTtfkck1TqPcdjLkiFvPeUzhKjrifogsOJnjiRFeoDR+O4nNr3a+mjAyPdkteTAJOCNFh2RwqG7JL+WjTCb4rOMv5mw86DVIVAzN7xfDQjO6ERvjzwZIjvL4jr/ELKPDzoSnuuXCtkqrCqW/hwKdwaClYXWXANA3q6Yn5XDLVWSXYiwuAAgB0gYEETZzg6n4cNRLdLYaat0jACSE6FE3TOFho5t/bT7F8fxFm+4V76lEOhVFhwfxgQlcGDI5Bd9H6aY/e0YOVB4rZb6lDUy50TT52V/OXmGoWpYdcV2oHPgezK7g0DertsVSbe2I+dAZ7cRmwHwBdQABBEya4rtRGjULn64UFUZuZBJwQokMoNdezeG8Bn27P58S5Wvf2QBX6aEbu6BNLxtQUOkVffg01nU7H248PcVUyaa1dk1WFcPBzV7CVHgRcoWatDcVc0wtztgV7cTngGkii+PsTPH4cwdOmETRmDDq/NlQe7DpIwAkh2q16u5O1h0r4fFc+3x6v5PxwEb0GqXY9I8OCuXt8Mj0GR2PwufY6agmdg/jFsBR3LcpW0TVZX+Wap5a1CE5uBTRXqFX7Ya7pTfUxB7aiCuAkAIqfH0HjxmGaNo2gsWPQ+Tddg669kFGUN+DiaQK5ubkyilKIVkjTNHadPMvivQUs31+ExXahCzLOoaOv08DMfp0ZMiHJvVxNm+OwuSqKZC2CnDXgtAJgrTJgruqO+aQOW9EZ9+GKry9BY8diyphG0Nix6AJa0UrfN+hGRlFKwN0EmSYgROuTf6aWL/YW8MWeAvIvKmpsUhXSbHpGmIIYMy6RnsNi8A1oI+upXUxVXbUfsxbB4S9dw/xpCLXKBMz5/tiKz7kPV3x8CBwzGtO0DILGjUMf1AbrXl6GTBMQQnQI1fV2Vh0o5os9hew8eeGKxahBD5uePg4Do9Oj6TcuntjundpmyazynIbKIp+5JmQDVrOe6tIYzEWdsBabAStgRTEaCRw92nWlNn48+qBW0IXqRRJwQog2xalqfHusgi/2FrD2UAn19oY7axokOXSk2fQMCAxgwOQ4eo2MJTCkDY4GrC5xjX488CkUu0Y52qr1mIvCMZeEYy2uaTjQDEYjQSNGEJwxjeAJE9BLr5KbBJwQok04WlrN53sL+HJfIaVmq3t7mNPVBdnbpie9VwRpY+Lo0ie80RD/NsFa7VoBO2sRnNgMmoqtRo8534S5NBJryflu1xowGAgcMRzTtAyCJ05AHxLi1aa3VhJwQohW66zFxrL9RXyxt4Csgir3dj8Netr0pNsMJPkZ6T0mjrTRsXSKamODJ5x2OPa160otexU46rDV6KnO98dcGkF9ib3hwDrQ6wkcNgzT9AyCJ05E36mTV5veFkjACSFaFZtDZVNOGV/sLWBDdhl2p2scnA5IsevobTPQ1a4jLjmEPmPj6DowCoPx2kP8Ww1Ng4Ldriu1Q4uhthK7RY853w9zUTz1ZecnM9hBpyNw2FCCp00jePJkDKGhXm16WyMBJ4TwuvPVRb7YWynBIOoAACAASURBVMCy/UWcsdjc+6JVHWn1enra9YQY9XQfFkP6mDgiE9rYEP+KY64rtaxP4ewJ7LU615VaYWfqys4PflFBpyNgyBBM06YRPHmSx9ZK6wgk4G6ALJcjRPMqNdezZF8hi/cWkFta494erOjoUacj3aYnUtUR2jmQ9DFx9BgWg6+/lz+2Di6G1c/D9Dcg7a6rH1tT5jo+axEU7cVe1xBq+VHUlV/0PhSFgEGDCM6YhmnKFAwREZ59Dx2EzIO7CTIPToibd766yBd7C9l6tBy14RPIqCikOvX0qtXRxaHDoNeRMiCSPmPj6NytlQzxrynH/NwQSnYYiRlmx/SHnRAU2fgYmwWyV7pC7fj/Z++8w6Mq0/7/OdOSzKT3TklCgFBEei/SgkhHUOB1XXdd+2t7retiWVH3t7rrrl3fdd8VFAQpUhQE6b2XQAJJgJBG6iSTSTL1/P44qRSFYVIIz+e6vJLznGfOuQ/CfM/9PHfZjL1SVpYfL+ipKmxQtFiS8Op9uxIoMnYM2tDQ5n2WmxSRBycQCFoVDauLrD2Wh8lirzvXUacj3iiTaFPjKUt4B3qQNDSKroMj0fu2oir2sox98WPk7dLhtEnk7dShX/I4mgcWg8MOmVuUJchTa7CbqjBd8KT8gj+VBY3TFLx69cI3eTw+48ahDbtJG6TeJAiBEwgETUZtdZHlh3LIKqkvcBzioaWrRUVCOQQ4VSBBbNcgug2Pol23IFSqVuCtXYJ84jvyF+/FafcEJJx2yP9mD9EeMyDvCPbiYkzZnpRneVFZ6AcN1sa8evZUlh/HjUMbEdFiz3CrIQROIBC4FVO1jR+O57PsUDb7ztZXF/HSqOjl6UW7i3aijBISEp7eWroMiiBpaBR+Ia246G9FIaYP/gdTdgMbZQlTthe53+zDXqHBXBAGcr0we/boge/48fiOG4s2KqoFjBYIgRMIBDfM1aqLSECPQG86mSCy0I4OJ6AivKMf3YZHEXd7SOsP8Zdl7IsfJm+XDsUta9zSuyyjvsajZ1KSsvw4fjy66OjmtlRwCULgBAKBy1ytukg7fy/66DwJP1eN3qhEHWs8NCT2C6Pb8CiCo2+CEH9ZhtzDyDvfJ++bAzhtytJkYyRARudrJ+bTz9H1GtkChgquhhA4gUBwXVytuoifl5ahYX50KHLica4SiWoAAiNrQvz7h6Nr6RD/a8FcrASLHPoKZ85JilINVOT8UrSehLVci+wtPLbWxk3wt00gELQ0V6suolFJDGkfRA+06FNNOPLMAKjUKuJuD6XbsCgi4v1aR4j/L+F0QMZmOPwfSF1HdYmMMd1A2bkwnPbampaXLk/WIMn4jByOR0JCc1osuAZuWODKyso4dOgQRUVFxMbG0r9/f3fY1SoRid6CtsCaY7m89n0Kr07qxp09rh7R90vVRZIifBke7kdktpWyQ+UAOACfQE+ShkXSZVArC/G/GiWZcHgRHPkapzEX0wUvStN9qSqqD+3XtWuH7113UfKvz3FWVnPpHpxK70X4Gwua3XTBr+NyondhYSFPPvkkS5curfvCv++++/jXv/4FwMcff8z8+fNZsWIFgwcPdp/FrQCR6C24WSmqsDDqr1sor7bj66nh52dHEOzdOE/rYnk1Kw/n8N0l1UVCfDy4s0sY3WwaKg4VYy6rETwJ2iUpIf6xSa0zxL8R1ko49T0cXgjntmM1qSlNN1B2zoDDUmO7RoPPHXcQMHsW+gEDkCSJ8rXryHnmmcsuF/W39/BNTm7mh7h1afJE75KSEgYNGkRGRgbdu3dn8ODBfPLJJ43mTJo0iccff5ylS5e2OYETCG5GZFnm5RXHMVuVF1Kz1cEfV57gk7m9r1pdRKdRMbZrGMND/dCnmzm/sYiLNSc9vbV0HRxJ0tBIfINbcYg/KAEjOYfg8Fdw4jvkqnJMOZ4Y04MwX6wXeE1EBAF3z8Rv+vTLKov4TEjGZ80qTFu2KukANUuTQtxaLy4J3BtvvEFGRgbz589n/vz5AJcJXFRUFF27dmXr1q03bqVAILhh1hzLY33Kxbpjh1PmxxP53Pv5Ho5nlzWqLtK7XQCTukUQVwHn91ykYNv5unMR8X50GxZFXK9Q1NpW3nPNXKSUzDq8EApOYjOrMGYaMJ6NxF6bdy5JGIYNJWD2bLyHDUNSXzltQZIkwv+8APOY0Tgrq8TS5E2ASwK3atUqEhMT68TtasTGxrJv3z6XDBMIBO6jqMLCyyuO1wS1N2ZXRjEAUf5eTL89imFh/pQdLSH92/Mcrcln03qoSewfTrfhUQRFeTev8deLww4ZPyveWtoPyA4b5jwPSjODqcjR1f0BqIOC8J8xA/+ZM9FFX1sitiYoiIg3F5D/5gLC//iyqPTfynFJ4HJzc5k8efKvzvPy8qK8vNyVWwgEAjchyzIvLT9OhcV+mbiBEjLRu50/r3Zvz6ntuew9n1N3LijKQLfh0XTqF4bOs5UHXRdnwBElYARTHvZqFcZMPcZzYdjKnXXT9P37EzB7Fj533IGku/5AGN/kZLEseZPg0t9YPz8/cnNzf3Veeno6YaKYqEDQYlwoqeTjLelsOHnxqnNk4MB5I98dP0WwU4VKIxFfE+IfHtfKQ/ytZjj5veKtnd+JLENloQ7j2TDKszTgkAEnKl9f/KdOwX/WLDw6dmxpqwXNhEsCN2DAAH788UdOnTpFly5drjhnz549HD16lDlz5tyQgQKB4PqwO5xsSi3g671ZbDtTSF2c9NXTuIi3qegYqCdpaBRdBkXg5dOKQ/xlGXIOKqJ2/DuwmnBYJcrOGSjNCsFaVJvOIOPZswcBs2bjmzwelVcrD4QRuB2XBO6ZZ55hzZo1TJo0iS+++IJhw4Y1Or9nzx7mzp2LWq3mySefdIuhAoHgl8kurWTJ/gss2X+BAlN92azBcUG0NzpZVlSK5fJSiuhkmBMaxNzn+iK15hD/isKagJGvoDAVWYbqEi2lF6Ipz5CQbQ7AiqTX4zdxIgGzZ+HZtWtLWy1oQVzOg/vb3/7G//zP/yDLcl1Ogp+fHzqdjsLCQgD+/ve/8/jjj7vV4NaAyIMTtBZqvbVv9mWx9XS9txZk0DGjTzT39I3FxyKz+I19pGrtrDbYLrvGXWYtnW0aZv+pH0GRrSyAxGGHjE1w6D9w+kdw2nHaJMqyfTFeCKM6tz5PzyMhAf97ZuM3aRJq71b2HAK30SwNT5966in69u3L22+/zZYtW5BlGaPRiE6nY9SoUbz88suMGDHC1csLBIJf4KreWnwQ9/Zrx5iuYeg0Kkwl1exenw5Aok1NqtVButZZm8ZFvE1FF4eGDr1CWpe4FWcontqRb6AiH4BqowZjXjxlp+04q6xABZJOh8/4cQTMvgevXre17v1CQbNzQ2FRQ4YMYc2aNTgcDgoLC3E4HISGhqLVat1ln0AgqOFavLX2wUrrFmu1nT1rz3Jk4wUcNaH+arWKMVU6sjTVWFCWJsdU6dB5aRh+T2ILPVUDrGZIWankrGXtApQSkaaCEEqzwqg6WwIoyWvadrEEzJqN39QpaAICWtBoQWvGLXG/arWa8PBwd1xKIBBcwi95a/f0i2Vs13B0GiXh2ulwcmpXHntXn6WqXAm2iIj3Y/CMBMqLqtjwRQpjq7Rs8rIxukqHQZYYfm9iy9WNlGXI3l9TYWQ5WJUlR2uFltLiLpSdqMRhqgRKQK1uXD5L1cqTzAUtjksCd/LkSVauXMmdd95Jz549rzjnyJEjrFu3junTp5OY2AreDt2AKLYsaC5+zVub3TeWDsGGRp/JSilm53fplOQqFf39QrwYOC2OjreFIEkSoe18SD9wEelYEZ3LNUgq6NArhIQ+LZDKU1EARxcr3lpRGgCyE0xlsRizQjGfygWKANCEh+N/90z8p89AGxb6CxcVCBrjUpDJH/7wB7788kvOnTtHZGTkFefk5ubSrl07/vCHP/DBBx/csKGtCRFkImgqrsdbq6U4p4Jd36WTdbIEAA+9hr53dqDb8CjUl8ytLLeyaP4erFV2PPQa7n11QPN5bw47pP+kiFpNwAiAzaLHWNYT49Ey7MVGZa4kYRg6pL58lqaVJ5kLmo0mDzLZsmULvXr1uqq4AURGRnL77bezadMmV24hENwy2B1Ofk4t4Ovr8NYAzGUW9q0+y6mducgyqNQS3UdE02dCezwNV94H1/vqGDEnkR3fnmHorE7NI25FZxRRO/oNVCgJ57IMZls3Ss8HUnE4E5xKrUt1YCD+06fjP+tudNGigajgxnBJ4LKzs+nVq9evzuvQoQPr1q1z5RYCQZun1lv79sAFLpZfm7cGYLM6OLrxAofWn8dmUZbLO/YKYeDUOPxD9b9634Q+YU2/LGmpgJMr4dBXcGFP3bBdCsZY0QvjgSJseRcBxevU9+1LwD2z8Rk92qXyWQLBlXBJ4LRaLRUVFb86z2w2u3J5gaDN4qq3BiA7ZU7vy2fPqkwqShVBDG3nw+AZCUQm+DfXI1wdWYYL+5SAkZQVdQEjMiqqdIMozfShfE8K2FMAUPn44Dd1CgGzZuERF9eSlgvaKC4JXFJSEjt27KhL7r4SRqORbdu2tZkAE4HgRnDVW6sl53QpO5elU5hlAsA7wIMBU+Lo1Des5auPmC4qy4+HF0Lxmbphh6EjZeZelO7NxXo2s27cs0cPAmbNwndCsiifJWhSXBK4e+65hyeeeIJZs2bx73//+7IUgYKCAu677z4qKipELUrBLcuNeGu1GC9Wsmt5OmePKhGFWk81vce3o+eoGDS6K/ctaxYcNjizoSZgZD3INZHFWj1VvqMoTddTvvIAcvVuACQvL/wmTsR/1iy8uiW1nN2CWwqXoihtNhsjRoxg9+7dGAwG7rrrLuLi4pAkifT0dFavXo3ZbKZ///5s3boVXRtbUxdRlIJf4ka9NYDqChv7157lxNYcnE4ZSYKuQ6PoN7FDy+WsARSeVpYgjy4Gc0HdsDO0D2WVPTHuOkv1ydS6cY+EePxn15TP8vFpCYsFbYzr+f51uRZlRUUFjz32GAsXLsTpdDY6p1KpmDNnDh988AE+bfAvtRA4waXUemvf7Mtii4veGoDD5uT41mwOrDuHpVIJo49NCmLQ9Dj3ldI6sRx+eB4m/AWSpv76fItJ2VM7vBAu7K0fN4RQHZKM8bSOsg3bcNbsy0taLT7jxxMwexZet98uymcJ3EqzCFwt2dnZbN68mQsXLgAQExPDyJEjiW7DIb5C4AS1ZJdW8u3+Cyy5xFsbFBfEvf1jGdM1DA/Nry8lyrJMxqFCdq9Ip7yoGlCajQ6enkBM10D3GVxRCB/0huoy8PSDxw6Cd8iVDIKsPYqopawAW03AmKTG2WE0pqrulG49RdWhw3Uf0cbGEjDrbvymTRPlswRNRrMK3K2IELhbG3d5a7VcPFvOzmVnyMsoA5Rctf6TO9J5YAQqdwaQyDIsmQdp65Q9M0kNne+EWV/VzzHlNwgYSa8fD4rHGjWJ0pMyZWs34CgtVcbVanxGjcR/9mwMAweK8lmCJqdZugkIBLca7vLWaikvrmLPykzO7FeSnzVaFbeNiaXX2Fh0nk3wTzNlOaSupjzLk/xDwYTfXoav/D0cWwpaL0XUzmxoEDBiQO48GZMlCeOmI5g/XFx3KU1YmFI+a8YMtGEtUOpLILgGbuhf0Y4dO9i2bRt5eXlYLJYrzpEkiU8//fRGbiMQtBju9tYALFV2Dv14jqObsnHYnSBB5/7h9J/cEe8AzyZ4CpSlyTVPYa9Wk7ffH6dNIm+/P/rQAjTLf4/S7ruGmP7YYidhTLFgfP977AVblHFJwjBkCAGzZ+E9fLgonyVo9bj0N7S8vJypU6eyZcsWQNk/uBpC4AQ3I+721kCp9H9yRy771pylyqQ0Ho3q5M/gGQmExDZhMJYsw5qnkKsryD/gi9MuARJOO+Qf8CN6SCmodch9/4DZ3o3SH3dQ8d4nUFNUXCmfNQ3/u+9GFxPTdHYKBG7GJYF74YUX2Lx5Mx06dOChhx6iU6dOeIsOuoKbnKbw1kB5ATx/ophd36VTmq/0M/MP0zNoWhztewQ3fZRh3jFIXY0pyxNTdoPEalnClO1FaYYFp1VF6eYd2HK/qzut79MH/9mz8Rk7BlUbS/UR3Bq4JHArVqwgNDSUffv2ERQU5G6bBIJmJcdYxZJ9WW711mopyjaxc1k62alKUIanQUvfiR1IGhaJWt2EARmyDDmHlICR48uwV6vI2++PshTZUFBl8vf71YxdVMpnTZ5MwOxZeMTHN519AkEz4JLAGY1GJkyYIMRNcNNyNW8t0KBjZu9oZvdzzVurxWy0sPf7TE7tzgMZVBqJHiNj6JPcDg99E3a8Lz0Px76FY4vroiBlGfIPh9ctTTZGOVb5eBP2wgv4Jiej0v96wWaB4GbAJYFLSEigqKjI3ba0ekTD05ufX/LW7ukXy9gk1701AJvFwZGNWRxafx67VSmAEN87lIFT4/ANbqK6i9VlkLISji2B8zvrxzVe0GUiFt+hmJa8/YuXcJoq8OrRQ4iboE3hUh7cZ599xhNPPMHBgwdJSrr16sqJPLibi6b21kCp9J+6J5+9qzIwl1kBCOvgy+AZCUTEXbkg+Q3hsEH6JsVTS10HjlqxlqDDUOgxG3voIIxrN1K6bCn2rAtXv5Zajc+oUUT/8x/ut1MgcDNNngf34IMPkpaWRnJyMgsWLGDMmDGEiVwYQTOx5lgur32fwquTunFnj4irzmtqb62W7NQSdn6XTtEFpVSVT5AnA6fGEd871L0BJLIMuYcVT+34MqhssIoS0hl6zEJOmk7FsbMY/3cZFZvfqouElLy8kO12sNkaX1OSUBkMhL863312CgStBJcErrZ4st1u57777gOU+pNX+scsSdJVc+QEguulqMLCS8uPU15t58Xlx+jfMZBgb4+6883hrdVSmm9m1/IMzh1ThEbnqab3hPb0GBmNRuvGSv/GC4qoHVsCRafrxw0h0G0G9JyN1RlM2fLlGF/5Dfb8/LopXrfdhv/MGfiOH0/F1q3kPP1M42vLMhGvvYpG7KcL2iAuCVxoqJvfTAWCa0CWZV5ecRyzVfFKzFYHf1x5gk/m9la8tf0X+Hb/BfLLq+s+425vDaCqwsr+1Wc5sT0X2SkjqSS6DY2k78QOePm4KZy+uhxOrlJE7dz2+nGNJyROgJ73IMcMwbR1O8ZXPsC8axe1aq7288NvymT8pk/Hs1Onuo/6JCfj88MPmH7erHh2NUuTvsnJ7rFZIGhliFqULiD24FqG1Udzefybw5eNJ0X4cDLf1KTeGoDd5uDY5mwO/nAea5VS6b99j2AGTYsjINwN93HYIePnmn21tWCvF2raD4Ues6DrJCy5xRiXfUfZypU4SkrqpugHDMB/5gx8Ro9G5eFxhRuAvbiYjPHJOE0mVL6+xP2wTnhvgpsKUYtS0OYoqrDw8orjSDQqKgVASp7S5bopvDVQPMf0gwXsXpGBqVgRneAYbwZPjye68w1W+pdlyDtav6/WoMcawZ0UUetxN06PEMrXr8f47qNUHTxYN0UTEoLftGn4T5+GLjb2V2+nCQoi4vXXyH9zAeF/fFmIm6BNIwRO0OqpW5q0OC4TN1AyuYYkBPPVA/3dfu/8zDJ2LjtDfmY5AAY/Hf0nx5E4IPzGKv2X5cDxb+HoEig8VT+uD6rZV5sFkbdTfeoUxn/8m7LVa3CaFCFHpcJ7+HD8Z87Ae9iw664J6ZucLJYlBbcENyxwaWlpnDlzBpPJdNWalPfee++N3kZwC3Mqr5z1KRevel4Gtp8p4vRFE53C3FPTsbyoit0rMkg/qHhUGp2K28e147bRsWg9XPQOLSY4tVqpLnJ2O3W+qNoDEpOh52yIH42jsprytWsxfruA6pMn6z6ujYrCf+YM/KZOFRX8BYJrwGWB27x5M48++ihpaWlXnSPLMpIkCYETuIQsy2xJK2TBupO/OE8tSYxJCnOLuFkqbRz44TzHNl/AaZdBgi6DIug/qSMGvyvva/0iDjuc3QJHF8OpNWCvqj8XO0gRta6TkT39qDp8GOMf51P+44/IVTXztFp8Rt9BwMyZ6AcMEP3WBILrwCWBO3ToEMnJyciyzN13383Jkyc5ceIEzz77LJmZmWzatImysjLuu+++Nt3ZW9B0nMgpY8G6U+zKKAbA10uDxebEanc2WqaUAIOHmj9P6XZD93M4nKRsy2X/mrNUm5VcsejOAQyeEU9wtAvCmX9cEbXjS6GigfcZGKeIWo+7IaA99pISyhavxLhsGdaMjLppurg4/GfMwG/KZNEdWyBwEZcE7q233sJms7F27VrGjx/P/fffz4kTJ3jnnXcAKCkp4cEHH+THH39k//79bjVY0LbJMVbx1/VprDicA4BOreL+we15ZEQ8284UXhZFKQMLpnVvlAt3PciyzLljRexanoHxolLpPyBcz6Dp8bTrFnR96TDlefX7agUp9eNeAXX5akT1RpZlzLt3Y1z2PqaNm+qSryVPT3yTk/GfOROvXreJVByB4AZxSeB27NhBr169GD9+/BXPBwYGsnDhQtq3b88rr7zCl19+eUNGCto+ZVU2PtqSzpc7z2G1KzUcJ98WybNjE4kJVOojTuwRwZpjuWw8VYDDKaNWSYzpGsbEHpEu3bMwy8TO786Qk2YEwMtHS7+7OtJ1cASqa630b6mA1DWKt3Z2K8iK7ah10Gl8zb7aGNDosF28SNknn2Bc9h22nJy6S3gmJSnJ2HfeidqnCfvCCQS3GC4JXElJCcOHD687rq1sUllZib6mWKunpyfDhg1jw4YNbjBT0Fax2p0s2nuef2w6Q2ml4skM6BjISxO60CPav9FcSZJ4c2p3dmdsobzajkHn2tJkRamFvasySN2bDzKoNSp63hFD7/Ht0Hldwz8Jp0MRs6NLlKARm7n+XMwAJQIyaSp4BSDb7VRs24bx26VUbNsGTkUAVT4++N01Ef8ZM/Ds2vW6n0EgEPw6LglcSEgI5eXldcfBwcEAZGZm0q1b/RdOdXU1RqPxBk0UtEVkWeaHE/n85cdUzhUrS4Pxod68mNyZUZ2vXikn2NuDBdO619WivJ6lSWu1ncMbsjjyUxZ2myI0CX3DGDClI75B11Dp/2JK/b6aKa9+PKBD/b5aYEflXllZGJf9m7IVK7AXFtZN9erdW/HWxo1D5dVE3QUEAgHgosDFx8eT0WBDvG/fvsiyzGeffcY//qFUJM/MzOTnn3+mY8eO7rFU0GY4eL6EN9ee4lCW8vIT7O3B02M6cXefaDTXsDQ4sUfkdS1LOp0yqbvz2Lsqk8pypdJ/RJwfg2ckENbhVyrRmPIVQTu6BC4erx/39Idu06DnPRDdFyQJp8WCae1ajEuXUblnT91UdWAgflOm4D9jOh7i34NA0Gy4JHDjx4/n5Zdf5uTJk3Tt2pUJEyYQGxvLhx9+yIEDB4iJiWHjxo1UVVVx//33u9tmwU3K2SIzf/kxlR9OKMWAvbRqHhzWkQeHdcTg0TQ1By6cVCr9F+colf59Q7wYNDWOjr1Crh7EYTUrpbKOLobMzfX7aiotdBqnVBfpNA40ivdoOXMG47JllK1chaOsTJkrSRgGDcJ/5kx8Ro1E0rmpRqVAILhmXPpW+a//+i98fHzqGn/qdDpWrVrFzJkz2bNnD3v27EGSJH7zm9/w5JNPutVgwc1HidnKPzadYeGe89idMioJ7u4Tw1NjOhHm69k098w1s2t5OudPKGkGHnoNfSa0p/vwaNTaK3iJTqdS1PjoYjj1PVgr6s9F91WWIJOmgV4pzeU0mylftQbjt0upOnq0bqomPBz/adPwmzYNXXRUkzybQCC4NtxebPnEiROUlpaSkJBAeHi4Oy/dahDFlq+NapuDf+08y8ebMzBZlOLEIxNDeCG5C4nhrkcLnjlwkR3fnmHorE7E9w5tdK6y3Mq+NWc5uUOp9K9SSXQbEUXfCR3w9NZefrGCU/X7auX1kY34t6vZV5sFQXGAsm9YfeIExqXLKF+zBmelsneIWo33yBEEzJyJYcgQJLUbW+UIBIJGtGix5YZBJoJbE6dTZsXhHN7dkEZumVKcOCnSl5cndGFQfPANXbuy3MqWRWlYq+xsWZRKZII/el8ddquDoz9f4OCP57FVKysLHW8LYeDUOPzD9I0vUlGgFDY+tlgpdFyLhx90mwo9ZkPsAKhZwnSUlVG2eg3GZcuwpKbWTdfGxuI/Ywb+U6egCQm5oecSCATuRxRbFriVnelFLFh3ipRcJco20s+TZ8clMuW2qBsrToziQW39OhVbjTdorbaz9etU4m4PZffKDCpKlMa6IbE+DJkZT2RCgwogtiplX+3YEkjfBLIigqg0kDC2Zl9tPGg96+5VuW8fxmXLMK3fgFzTtFfS6fAZOxb/mTPR9+0jSmcJBK2YaxK4BQsWAPDwww8TEBBQd3wtSJLEiy++6Jp1gpuGtHwTb/1wii1pSki8j4eGR0bGc//g9ni6qbt1+sECMo8UEe+5kyE+X7C9/HdkHBlM5hGlo7Z3gAcDpsTRqW8YkkpS9tXO71Q8tZRVYDXVXyyqt+KpdZsGhnqv0l5URNnKlRiXLsN6/nzduEdCAv4zZ+I36S7U/o3z8wQCQevkmvbgVCoVkiRx6tQpOnXqVHd8Ldt3kiTVBaO0FcQeXD0Xy6t5b8Nplh68gFMGjUpi7oB2PHFHAoEG90UOVpZbWTR/D2pLEXOCH0MnmbHIBr4u+oAqpz+3j29H3wnt0ejUUJhWv69WdqH+In6xSq5az9kQnFA3LDscmHfuxLh0GabNm8GueIiSXo/fnROUZOwePUTpLIGgFeD2PbjPP/8cgIiICAA+++wz8Y/9FsdssfPptkw+35ZJlU15gZnQPZznxnWmvRu7aEPDpUkbo3w/QStVIUmgo4rhvp+yvux5KnNz0Bz6SfHWchvUq/Twha6TlcYEiQAAIABJREFUFVGLHQQNlhRtubkYv1uOcfly7Hn1iduePXvgP2MGvskTUHu791kEAkHz4fYoyluBW9mDszucLDlwgb/9dIaiCmVf6vZYf16+swu9291gd+urUJxTweI39hHvuYNx/u9edr7A2pFg7TlUUk2+mqSG+NFKyazECaCtrxgi22yYNm/GuHQZ5h07lI7agMrPD79Jk/CfMR3PxMQmeQ6BQHDjNHkU5XPPPYe/vz8vvfSSSwa2FqZOncqWLVu44447WLZsWUub06qRZZlNpwp4+8dU0guUHLH2QXqeH9+Z8d3Cm8yjt1bbObkzFy+VkRG+nyDLEpLU+J0sVJep/BJxm1JZpNt08G4c1Wg5e7Y+Gbu4uG5c36+fkow9dgwqD9c6EggEgtaJSwL397//nUmTJrnblmbniSee4Le//S3/93//19KmtGqOZRt5c+0p9p4tASBAr+W/70jg3v7t0GmaLorw3PEitn1zGlNJFeP9a5cmZcqzPMk/5Ef47WX4xlYjyxLOdsNQ//b7Rp93Vldj2rAB47dLqTxwoG5cHRyM/9Sp+E+fhq59+yazXyAQtCwuCVxUVNuo0DBy5Ei2bNnS0ma0Wi6UVPLXDWmsOpILgE6j4reDO/DIyDh8Pa+QNO0mKsut7Pj2NGcOFAAQE3SROO1eAOzVKvL2++O0SeTt90cfWoDG04k6a6uStB3aherUVIzfLqVs9WqcpprISZUKw9AhBMyciffw4UjaprNfIBC0Dlx6/Z4yZQpbt26loqLi1ye7yLZt27jrrruIjIxEkiRWrlx52ZyPPvqIDh064OnpSe/evdm+fXuT2XMrUVZpY8G6U9zx7lZWHclFkmBaryg2PzuCF5I7N5m4ybLMyZ25fP3qHs4cKECS4LYxsUyY6QEqLbIM+Qf8cNolQMJpl8g/4AeSGkeHOyndfJyzM2ZydspUSr/+GqfJhCYyguDHHyN+00ZiP/0Un9GjhbgJBLcILnlwr732Gj///DMTJ07kn//8J927d3e3XZjNZnr27Mn999/P9OnTLzu/ZMkSnnzyST766CMGDx7Mp59+SnJyMidPniQ2NhaA3r17Y6lJ0G3Ihg0biIx0rUlmW8Zid/DV7vP88+d0yqqU3myD44N4MbkL3aL8mvTepflmtixKI/eM0mEgJNaHUXdHEnzsdVi5GADTBT2m7AYtZmQJU7YX5zbrqDaeQq6uiZ7UavEZNQr/mTMxDBwgSmcJBLcoLgncjBkz8PHxYdu2bdx2223ExMQQGxuLp+flhXMlSWL9+vXXfY/k5GSSk5Ovev69997jgQce4He/+x2g7AuuX7+ejz/+mLfeeguAgwcPXvd9r4TFYmkklA174bUFZFlm7fE83vkxlQslVQB0CvPmxQldGNHpF6ruuwGH3cmh9ec58MM5nHYZjU5F/0kd6dHxPKpV45Q8NkmFvecj5H2/DqgCGttTla8GqtF16ID/jBn4TZmMJiioyWwWCAQ3By4J3MaNG+t+l2WZrKwssrKyrji3Kb4crVYrBw8e5IUXXmg0PnbsWHbt2uX2+7311lu89tprbr9ua2D/OaU325ELiucU6qP0ZpvR+9p6s90IeRllbF6YSmme0hE7NimQ4Xe3x/fou/CffwIyBLRHnvIJ+X/9GqfFxqXiBoAk4dWnN+3+8x+RnykQCOpwSeDOnDnjbjuui6KiIhwOB2FhYY3Gw8LCyM/Pv+brjBs3jkOHDmE2m4mOjmbFihX07dv3snkvvvgiTz/9dN1xeXk5MTExrj9AKyCjsIJ3fkhlw8mLAOh1av4wLI7fD+uAXte0JUotVXb2rMjgxDaler+Xj5ahd3ciPqYQafmdcPGEMrHXPBj/FpbzeZh+2nj1C8oyVfsPYE1PxyMh4erzBALBLYVL32RxcXHutsMlLn1bl2X5ut7gr3Xp1MPDA482kiNVVGHh/Y1n+HpfFo6a3myz+8Xy5OgEQn2apjdbQzIOF7Bt8Wkqy5TO2l0GRTBoWkc8j30On78GDivog+Cuf0CXiQA4q88h6fXIte1pLkWtxmfUKCFuAoGgETdlN4Hg4GDUavVl3lpBQcFlXp1Aocpa05ttSwYVNdX47+gcygvJnUkIc70327VSUVrNtsWnOXtUKYzsF+rFiDmdiQ6rgGXTlWajAAnjYPIH4B2KbLNR9OlnFH38MTgcSvuaSwvvSBIqg4HwV+c3+TMIBIKbC7cIXEVFBSaT6arFl90dsajT6ejduzc//fQTU6dOrRv/6aefmDx5slvv1ZAPP/yQDz/88KYqHu1wyiw/lM27G06TX670Zuse5cdLE7owMK7pAzGcTpmUbTnsXpmBrdqBSiVx+/h29B4fiyZ1OXz8LFjKQKuHcW9C7/tBkrBkZpL73PNUn1CWK32Sx2MYNIj8V/7U+AayTMRrr4qgEoFAcBkuC1xBQQF/+tOfWLVqFQUFBVedJ0kS9prq7NdDRUUF6enpdcdnz57lyJEjBAYGEhsby9NPP828efPo06cPAwcO5LPPPiMrK4uHHnrIpee5Fh599FEeffTRulporZ1tpwtZsO4UqflKsnOUvxfPjU/krh6RN9yb7Voozqlg88JULp5Vok7DOvgycm5nggKssOr3kLJcmRjVG6Z+BsHxyE4npV99RcG77yFbLKh8fQmf/yf87rwTWZYxb9uG6efNikdXszTp+wvRtgKB4NbFJYHLz8+nX79+5OTkEBYWRnBwMIWFhfTt25fMzEyKi4uRJIl+/fqh0bimoQcOHGDkyJF1x7VBHvfddx///ve/mTVrFsXFxbz++uvk5eXRrVs31q1bR7t27Vy6X1viVF45C9adYvsZZTnQx1PD46Pi+a+B7uvN9kvYbQ4OrDvH4fVZOJ0yWk81A6fE0W1YFNLZLfDxI2DKVYoiD38Ohj4Lag223FxyX3qZyj17ADAMHkzEgjfR1iw7S5JE+KuvYt6zF6fJJJYmBQLBL+JSN4HHH3+cDz/8kPnz5zN//nzuv/9+/vOf/9Qt3f388888+uijhIWFsX79+jYToFFLa+0mkF9Wzbsb0lh2KBtZBq1aYt6A9jw+Kp4AN/Zm+yWy00rZsiiVsgIln65Dz2CGzU7E29sJG1+DvR8rEwPjYNrnEN0bWZYpW7mKi2++ibOiAsnLi7Dn/gf/2bOvGDRU/sMP5L+5gPA/vozv+PHN8lwCgaB1cD3fvy4JXHx8PE6nk4yMDCRJukzgAC5cuEDnzp15+umneeONN67/KVoxrU3gTNU2Pt2ayRc7Mqm2KS1j7uwRwXPjEmkX1Dz9zKorbOxcnk7qLqWvmsFPx7DZiXTsFQJ5R2H5g1CYqkzu8wCMfQN0BuzFxeTNn0/Fxk0AePXsSeQ7b4siyAKB4Io0ebucnJwckpOT696u1TWlkKxWKzqd4inExMQwcuRIlixZ0mYErrUFmdgcThbvy+LvG89QbFbC7vu2D+ClCV3oFRvQLDbIssyZAxfZ8e0Zqkw2kKDbsCgGTInDw0OC7e/B5gXgtIEhFCZ/CJ3GAmDatIm8P81X2tdotYQ89hhBD/wWycVlbYFAIGiIS98k3t7ejY79/f0BRfg6dOhQN67X68nOzr4B81oXrSXIRJZlfjp5kbd/TCWzUKkC0iHYwAvJnRnbNazZqnmUF1Wx9Zs0slKUNjqBkQZGzOlMRJwflJ6Dbx6CrN3K5M4T4a73wRCMo6KCiwveomy5EmTikZBA5F/ewbNLl2axWyAQ3Bq4JHCxsbGcP3++7jgpKQmAH374gUceeQSAqqoqdu7cSXh4uBvMFNRy5IKRBWtPse+cIipBBh3/PTqBe/rFom3i0lq1OB1Ojv6czb7VmditTlQaib4T2tNrbDvUagkOL4IfngerCXTekPwO3DYHJAnz3n3kvfgittxckCSCHvgtwU88gUrXPHuEAoHg1sElgRs1ahTvv/8+BQUFhIaGMmnSJPR6Pc888ww5OTlER0fz1VdfkZ+f36Rh+7cSWcWV/GV9KmuOKXtcHhoVvxvagYeGx+HThL3ZLqUwy8TmhakUZimpB5EJ/oyYk0hAuAHMxbD6CUhdo0yOGQDTPoWA9jgtFgrf+xslNc1ltdHRRL79Fvo+fZrNdoFAcGvhksDNmTOHc+fOkZKSQmhoKEFBQXz88cc88MADvPXWW0iShCzLdO7cmTfffNPdNt9SGCut/PPndP6z+xw2h4wkwfTbo3lmbCci/Lx+9fPuwmZxsG91Jkc3XUCWwUOvYdD0eLoMilCWRE9vgFWPgrkAVFoY+RIM/m9Qqak6kULuC89jTc8AwH/mTEKffx61d/MEwAgEglsTl6Ior8bZs2dZu3YtpaWldOrUialTp9YFnbQlmiOKstpW25vtDOXVSqL80IRgXkzuQtfI5o3cPJ9SzNZFaZhKlEooCX1CGXJ3J/S+OrCaYcMrcOB/lcnBiTD9c4joiWy3U/TZZxR99DHY7aiDg4n48xv4jBjRrPYLBIK2g9vTBNq1a8fcuXOZM2cOXbt2dZuhNxsNoyhPnz7tssCtOZbLa9+n8OqkbtzZI6LROadTZvWxXP7f+jSyS5Vcss7hPrw4oQvDO4W45TmulcpyKzuWnuHMfqXjgHegB8PvSaR992BlQvZBWPEgFNdUnOn/MIyeD1ovLJlnyX3hBaqPHQPAZ9w4wl+djyageaI7BQJB28TtAqdSqeoi83r16sXcuXOZPXv2LRtAciMeXFGFhVF/3UJ5tR1fTw0/PzuCYG8lEX5PZjEL1p3iWHYZAGG+HjwzNpHpt0ejbobSWrXIskzq7jx2LkvHUmlHkqDHqBj63dUBnacGHHbY/i5sfQdkB/hEwJSPIG6UUmpr0dcUvPsucnW1UmrrlVfwnXin6NUmEAhuGLcL3Pbt21m4cCHLli2jtLQUSZJQqVSMHj2aefPmMWXKFPR6vdseoLXjqsDJssxDCw+y8VQBDqeMWiUxpmsYz45N5O0fUtl4SvGUDDo1D4+I44EhHfHSNX1prYYYL1ay5etUctKUBqjBMd6MnNuZ0HY1z1mcoSRt5xxQjpOmwZ3vgj4QW14eeS+/jHmXkhpgGDSQiAUL0N6iL0ICgcD9NFklE5vNxrp16/jqq69Yt24d1dXVSJKEXq9n2rRpzJkzhzFjxrT5N3VXBW710Vwe/+bwZeO1XWDUKol7+sXw33d0IsSnecubOexODm/I4sC6czjsTjRaFf3u6kjPO6JRqVWKgQe/hPUvg60SPPwUYes+AxkoX72a/Df+jNNkQvL0JPR/niXgnnuQVM2TuiAQCG4NmrxUV+1Nli5dyqJFi9i2bRtOpxNJkggLC+Pee+9l7ty53HbbbS49QGvHFYGrXZo0Vdu50h/48E4hvDKxK/Gh3lc427TkZ5axeWEqJblK0nhs10CG35uIb3BNlGZFAXz/OJz+UTluPxSmfAz+MdhLS8mf/yqmDRsA8OzZg8i338ajQcK/QCAQuItmEbiG5ObmsmjRIhYuXMjx48eVC0sSXbp0Yd68eTz//PM3eotWxfUK3KVLk5eikmBsUjifzO3dFOZeFWuVnT0rMzi+LQdk8PLRMmRmAgl9G1RDSV2riFtlMah1cMefYMCjoFJh2ryZvFf+hKOoCDQaQh57lKDf/U6U2hIIBE1GswtcQ06ePMlXX33FF198Udc2p7XUbnQX1ytwafkmxv1926/O2/DUMDo1Q3dtgMwjhWxbfBqz0QJA54HhDJ6egKd3TdK4xQQ/vgiHv1KOQ5OU8P+wJBwVZgreeRvj0mUA6OLjiHznHbxqKtoIBAJBU9HkxZavRnFxMVu3bmXbtm2UlJS489KtAleLLXcK82ZcUthVPTi1JDEmKaxZxK2i1ML2JafJPFIIgF+IFyPmJBLdObB+UtZeJfy/9BwgwaDHYNQroPGgcv9+cl94EVtODkgSgb/5DSFP/jeqNtYSSSAQ3PzcsAdXXV3NqlWrWLRoEevXr8dutyPLMsHBwcyaNYt58+bRr18/d9nbKnDnHpyE0pC0YbpAUyA7ZVK257B7RQbWagcqlUSvsbH0mdAeTW2kpsMGW96GHe+B7AS/GGWvrcNQpdTW+/+g5MsvQZbRRkYS8fZbGNrY/1uBQNC6aXIPTpZlNm3axMKFC1mxYgUVFRXIsoyHhwfTp09n3rx5JCcnu9zNuy0S7O3Bm1O7XxZFKQMLpnVvUnErzq1gy8I08jNr8us6+DJiTmeCoxsEtBSmKeH/eUeU4x6zYcJfwNOP6pMnyX3+eSxnlIRuvxnTCXvhBdTezR8QIxAIBNfKdSnQoUOHWLRoEYsXLyY/Px9ZlpEkiSFDhjBv3jxmzpzZom1kWjsTe0Sw5ljuZXlwE3tENsn97DYHB384z6H153E6ZLQeagZMiaPb8ChUtYnjsgz7PoefXgF7NXj6w11/h6SpyHY7xZ98SuGHH4LNhjooiIg3Xsdn1KgmsVcgEAjcyTUJ3JtvvsmiRYtIS0sDFA8uMTGRuXPnMnfuXNq1a9ekRrYVJEnizand2Z2hVDIx6NT8eUq3JrlXzulStixKw3ixEoD2PYIZNrsTPoGe9ZPK82DVI5Dxs3IcN0ppSOobifXcOXKff4Gqo0cB8BkzmvDXXkMTGHjprQQCgaBVcs2lugCCg4OZPXs28+bNo2/fvk1uXGvlRost/1Ityhul2mxj1/J0Tu1U2uro/XQMm9WJjr1CGifgp6yENU9CVSloPGHM69D398iSROk331Dw//6KXFWFytub8Ff+iO+kSW0+gV8gELR+3L4HN3PmTObNm8f48ePFvpobmNgj0u3LkrIsk36wgO1LTlNlsgGQNCyKgVM64qFv0C+uugzWPQfHFivHET1h2ucQkojt4kXyXnoZ886dAOgHDCBywZtoI5tmCVUgEAiakmtSqyVLljS1HYIboLy4im3fnOb8iWIAAsL1jJjbmch4/8YTz+2AFQ9B2QWQVDDkaRj+PLJaS/mateS//jrO8nIkDw9Cn3mGgLlzRKktgUBw0yLcsevA1Ty4psLpcHJsczZ7v8/EbnWi0kj0SW7P7WPbodY2ECa7BX7+M+z6JyBDQHuY+inEDlBKbb3+OqYflDJcnt26EfmXd/Do2LFFnkkgEAjchdsrmdwKNEfD01+jMMvE5oWpFGaZAIiI92Pk3M4EhF/SJftiihL+f/GEctxrHox/Czx8qNi6ldw//hFHYRGo1QQ/8jDBDz6IpNUiEAgErZEWq2QiaHpsVgf7V5/lyKYLyE4ZD72GQdPi6TIoAqlhzzinE/Z8BJteA4cV9EFw1z+gy0ScZjMX35yP8dtvAdB17KiU2ureNBGdAoFA0BIIgbuJyDpZzNav0ygvqgYgvncoQ+5OwOB3SZK48QKsfBjObVeOE8bB5A/AO5TKgweVUlsXLgAQeN99hDz1JCpPTwQCgaAtIQTuJqDKZGXHsjOc3qs0RPUO8GD4PYm07xHceKIsw/GlsPZZsJSBVg/jFkDv3+C02Sj6618p/t9/gSyjiYwgcsFbGAb0b4EnEggEgqZHCFwrRpZl0vbks2PZGSxmO5IEPUbG0G9SB3Sel/yvqyqFNU9DynLlOKoPTPsMguKoTk0l97nnsZw+DYDf1KmEvfQiap/m6VwgEAgELYEQuFaKsaCSLYvSyEkrBSAo2puRczsT1v4Km6oZm2HlI2DKBUkNw5+Doc8iI1H82ecU/vOfSqmtwEAiXn8Nn9Gjm/lpBAKBoPkRAtcCnDlwkR3fnmHorE7E9w5tdM7hcHLkpyz2rz2Hw+ZErVXRb2IHeo6OQa2+JCfNVgUbX4O9HyvHgXFK0nZ0b6znz5P7wotUHVaKO3vfcQcRr7+GJiioOR5RIBAIWhwhcM1MZbmVLYvSsFbZ2bIolcgEf/S+OgDyz5axZWEqxTlmAKI7BzBiTiJ+IfrLL5R3VAn/L0xVjvs8AGPfQNbqMS5ezMV3/qKU2jIYCPvjH/GbMlmU2hIIBLcUQuCugxtN9JZlma1fp2Kz2AGwVtvZ+k0ad9zXhT2rMjm+JRtk8DRoGTIznk79wy8XJacDdr4PmxeA0waGUKVAcqex2C4WkPfHpzBvV6In9f37K6W2oqJu6LlbG2m7t/Pzl58y6v6HSBw4pKXNEQgErRSR6O0CriZ6nzlwkQ1fpFw27mHQYDEropc4IJzBM+Lx8tZdfoHSc0qprazdynHniXDX+2AIpnzdOvJeex1nWRmSTkfoM08TMG9emyu1VVlm5F9P/gFLpRkPg4Hf/u1T9H7+v/5BQR3iBUFwMyMSvVshtUuTV8JituMd6MGoeV2I6XKFdjSyDEe+hh+eB6sJdN6Q/A7cNgdHWRn5Tz9D+bp1AHgmJRH5ztt4xMc35eO0CLIs89PnH2KtrgLAWlXFxi8+YtIzL7WwZTcPlWVGfvrsAyyVZn76/J/EdO0mXhAEbZa29XrfSrl0afIyJAiJ8bmyuJmLYclcpW+b1QQxA+DhndBrLhU7dpB51yRF3NRqgh99lPaLv2mT4gaK55G+fzey0wmA7HRyZt8u0nZvb2HLbg6u9oIgELRVhMA1AyW5ZjKPFCE7rzJBhrNHiyjOrWg8fnoDfDQAUteASgt3zIf71+H0CCXv1Ve58PsHsRcWouvQgfaLvyHk8cfabB3JyjIjGz//ELg0UEbip88/oLLM2BJm3VSIFwTBrYZYomwGAiMNdLwtmLPHrixykgo69AwhKNJbGbCaYcMrcOB/lePgRJj+OUT0pPLQYXJfeAFbVhYAAfPmEfr0U6i8vJrpaZqfxp7HpVvGMhazmf889ziRiV1QqdRIKhUqtVr5r9GxCklVO97g90bHylxJpYyp1GqkmuvUf15Vc1xz7QbXUY5Vymcuu3/D66ganW9qGr8gNPwzVF4QYrp2F0uVgjaHELhmQJIkht/bmey0PVir7MR77mSIzxdsL/8dGZbB6Dw1DL8nUZmcfRBWPAjF6cpx/4dh9HxkWU3he3+j+IsvwOlEEx5O5FsLMAwc2HIP1oTIskxpXi45qSlkHNxLxoG9vzjfbCzlzN5dzWSdm5EkRUxVNWJaI6BXFeoGAno1ga37jFqNJEnkpJ7EUlnJlV4QrJWVrPjLGwyZNQ8PvR6d3lDzU49GqxPpJZcggnRuHkQUpQvcSBTl9n/tZE7wY+gkMxbZwNdFHzD0t4NJ6BUE29+Fre+A7ACfCJjyEcSNojrtNLnPP48lVcl585s8mbCXX0LdQq16mgKn00Hh+XPknDpBdmoKOaknr3nZUZIkQtp1oPuocTidTmSnA6ej5j+nA9npxOlw4qwZV44d9fOcTuSan7Wfk+UGvztrz9XOa3wd5dipHNeeqx2vvbbDiXzVNerWi0qtwUOvx0NvQFfzs9GxV70geugNDY7rxzQeHm1GJEUUb8sjoihbKfG3h+Dz05doq6uQJNBRxYSYfxPeoSf8617IOaBMTJoGd76L7OFHyRdfUPj+P5BtNtQBAYS/9iq+Y8e27IO4AbvVSn7GaXJST5KdmkJu2imsVZWN5qg1GsLjOxHVOYng2PZs/OJDrJWXLlNK6PR6pr/0eqv/opFl+RJRbCCUTgeyw1kvuFc476wRynrRrhHwq4l0zeccdjspWzZSnHNBici9Ap7ePngHBGKprMRSaVaWg2UZp8NOlamcKlO5y88tqVSXCWFDsdR5Nfi9do5Xw2MDOk/PFk95EVG8Nx9C4JoR6eQKwi3b6uIkVJKTcMtWJZDEYQUPP7jzXeg+A2t2NrkvPEHVwYMAeI8cqZTaCglpwSdwHUulmdy0UzXeWQr56adx2BtHleq8vIhM7Ep05ySiOnclPK4TGl19PqAkSax9/y+XXFlmzO8fa/XiBor9tUuLzU3SsFF1nseleBgM3P/ex43+DGWnE2t1tSJ2lWZF+KqUn3XHNT+tVTWi2HCs5qcsK55ttbmCanPFZfe+ZiQJnafX5ULY8NhL39jL9GoopAZ0ei9UKtf/7GuDdBr+GdUG6SQOHOr6swmaDCFwzUVFIax5iss3+VHELaY/TP9fZL9ojEuXcvHtd5ArK1Hp9YS9/BJ+06bdVMs8ZmMpOakpZJ9KITs1haLz5y5botP7+Sti1iWJqM5JhLRr/4tfQIkDh5K2azsZB/ciO51IKhXxfQaIL5drQO/nz+jfPcraf1z6gsAVXxAklapGQPSAay9Vsixjs9SKZFW9WFZdLoSWSnONUF4ulk6HHWQZa5UipqZil8wBQOvheZlAXr7E2nAJVjnndDj46bMPEEE6NxdC4JoDWVbEzVIByJRneZJ/yI/w28vwja0GSQJDKDarB3kPPYR56zYA9H36EPH2W+iio1vW/l9BlmXKLubXeWc5qSmU5uVeNs8vLLxO0KI7J+EfHnldoi1JEmN+/ygXUo5hqTSj8/Ji9O8eceejtGnORpg5F2YmtkCPSpZwSjJZoZWcjTCT2AT3k2q8Lp2nF1whxfNakGUZu81aJ3jKz1pvsrEQ1h1XXSKclZXYbVYAbJZqbJZqKC1x01PKWMyVLHntBW5PnowhIBDvgEAMAQEY/AJaxFsX1COCTFzguoNMLp6Ej5VoR3u1ioy1oThtEiqtTNydBWg8nYronYjFUV6BpNUS8tRTBP7mvhbfd7gSstNJYdY5xUNLPUlOagrmS78wJImQmHZ13ll05yS8A93TyUBEsV0/xVXF3LXiLuwVlUzZGoHOrsKqcbJieC6SwYNXB75KuCEcg9aAt84bg8aAQWdAq2obeZUOu+0S71ARyXqBvLpHWWUqo6rchT1ISULv64fBP6BG9AIx+Cvi5137MyAIvX8Amjaav3olfjz3I2/vfZsX+7/IuPbjrvvz1/P9KwTuOmhYbPn06dPXLnCyDEvmIaeuI2eHL6YcT5AlkGS8I6pRaaH8vJLH5tG1C5Fvv41np05N/DTXjsNuIz8jnexTJ8ipCQi5dC9HpdYQFhdPdJduRHdOIrJTFzy9vVuhSloIAAAgAElEQVTIYgGAxWEhpSiFwwWHWXhqIUVVRQC0z9PTLyWQvUklnI+o/MVr6FQ6RfC0hrr/vLXe6LV6vLXejcauel5nwKAxoL6B/a+WRJZlvn93Qd3S+GVIEr7BoQTHtsNcWorZWILZWHrluVfB09sHg3+A4gH6B2AIDFJ+BgQ2GA9E6+npxidrfmpftEw2Ez46H1ZPWU2Q1/W9+AqBa2JcShOoKKT8mX7kbL3KX1CViqA/PEjIww8j6a5QaLkZsVZVkns6tcZDSyH/zOm6JZ5atB6eRCZ2IaqzEhQSnpCIVufRQhYLAAorCzlSeIQjBUc4UniEk8UnsTuvUh7uCoTrw7E6rVTaKql2VLvdPi+N12UCeM1CWSOS3jpvvDReqKTmXdmoTQ+orjQ3qqUjA55XSBdwOh1UlZdjNpZiLi2hwliCubSUitISzKUldSJoLi25LNjql9B56etFsEb8LvMOAwLReelb3Z69LMs8teUpzu7dS58Uf/Z3MxLXbwB/G/m367qOSBNohdgtKvL2+wNVXFZuSpKI+fgjvIcPbwnTqCwz1oXr56SmUHAu87K3Ty8fX2WpsWbJMbR9R7G/0ILYnXbOlJ6pE7SjhUfJqci5bF6QZxC3hdzGedN5MssycV4hF08tqRkZM7LRF43NaaPSVonZZqbCVoHZZq77vdJWSYW18Vjt7/+fvTuPi6rq/wD+ucMy7IMgq4Kg4gYuKJa4BLjhXtli2qJiZZklmmnmhrhbLo+ZGqaZ9XOppyx9SlEzXBINF1xQAQUEEUS2GbYZmJnz+2NiZJxB1ln5vl+veRn33rlzhnQ+c84993s0HV8dshXSClRIK5Q9ycbiwMHGwkZjKNa27cneZHWv1MrMql5BYCNwhMvzA3F/X+wTbQFcnh+oNsGExzNT9LwcWwE+7Ws9L2MM4rJSlBUWoPTfwHscikUoKypQBGNxIaQSiXKiTdGD+09tr7kl//G1QMfHf1aHYXVAWtnZ6ywIYzNi8XfqX3jxuicspTz0u+aIg61O4qjvUYzwGaGV16SA0wHGGHKjoiCXVEG9liIAjkPxf3/WScAxxiB6lKccbrx/+6bGfywOLq4qMxydPNsa3DfClkRUKcK1R9eUvbPrj66jXKo6vMjjePBz9EMv116Kh0svtLFrA47jlENDpVWlYDVmAXLgYGthi0X9Fqmcy4JnAQFfAAFf0OS2V8oqVUOvshTl0n9DUlqGsspaQlTDNhmTgYEptzcVj+PVKyR54GFXyXcIdrNXm6TzY+kPaH2vGxz4j3sTHDjlvxfu33/zT/6sss2aA6zNwHm4whqusOE4uFYfxyn+3crElZCIRKgUlkIiFEEiEkEiLIFYqPhTIhJBXCyCVCyGtFKC4oc5KH6Y8/T3b24OKwd7WDk6wlrgACuBANYCgeLPVoo/bQSOsHKwB8fjPX4vT7ynp71PjuNQJC7CsnPLEHzDGRYyxXksZDz0u+GMaPto9HXr2+ChyvqgIcpGaOgQpTglBenjnq/zuPaHD4Hv59ccTVRicjkK7mcqJ4Pcv52E0gL1b9DObb2VvbM2Xfzh0No477czBYwxZJZkIjEvEVfyruDqo6u4W3xXJZgAwM7CDj1deqKna0/0cumF7q27w86y9uueR9OP4pPTn6ht/zzkc619g25OjDGIZWKNvcmnheKT28qqylBeVa72+6wPKwkPL57yVJmkI+YbVoUaMxkHG7EZrCWKh43ETPXnf//bqqr+IzByjqHCUoYKvgzlVoo/a/53OV+GCisZKixlYLWMHvs8sEFoovrnyunAArR/tv5DlTREaWD4fn6wHzYUJSf/AjStBm5mBvvBg5sl3GRSKR6m3VGG2YPkWxCXlqgcwzMzg5tvR2XvrE3nrrC2N52yX8ZGLBUjqSBJ2Tu7mncVRZIiteO87b3Ry7UXerr0RC/XXugg6NCgiRvhPuE4mnEUcVlxkDGZcmjSGMINUPQErM2tYW1ujdbWrZt0LjmTo0Ja8Tj0Kss09ibLqspwv/Q+YjMUQ5Nivhzx3QuVk3RqhlsbuzawNLNEzT5DdYhWb2NgKv9dE2Ps8fEanvfvf9R9DABmw1AFoBIMQuXrVgGoUh7HkyoCmy/mwBdzsJbwYCXmYCXmwUrMg7WYByuJ4sFjHGwl5rCVmANPmVDKwCC2lCvC7t/gK+fLUGUmQ8+7jmBgKj1YBoZnrzvioNNJ3Ol1Bx1bNe9SXxRwOsBxHNyjolB2/gLkpaWq5ZI4DjxbW7hHLW3UuavEYjxIva28/+xBSjKklRKVY8z5fHj6dVFeQ/Po2NnoZ2MZs7zyPJUwu1moPhnEkmcJ/9b+yqHGni49mzyEw3EcFvdbjH9y/kFJVYnGocmWonpo0tbCFq5wfeqxjDFI5VLlF4MMj3Jk1Jh9qukapimRy2QoFxajrLgIpdXXBJ+YKFNaVIhyYTHkMhmsK81gXVm/L17VQ5Xj0ro0e7gBFHA6Y+7sDI9lUcie87HqDsbgsSwK5s71+/CqKBGpTghJvwv5E71CKzt7tOnSTXn/matvB5iZ0/9qfaieDHIl74oy0B6Uqd8E39q6NQJdA5W9s65OXWFp1vyzaZ2tnbGk/xLlfUjauO5hamp+MajvNUxTwjMzg52TM+ycnOGG2kOIyeWoKBH9G36Pgy8/6x6Sz52u/fyMg016GfKz7qG1V7tmbTt96umQ3YgRKNm8CbYZWeABkAMo8/GGw8iRtT5HlJ+H7H/LXWXfvomC+5lqx9g7uyim6/875OjcxssgbxBvCYQSoWIyyL9hdi3/GiqkFSrH8DgeOrXqpAyzmpNBdGGEzwijGZY0FM7WzlgSvETtGiYDw+LgxfRFAYrybjYCR7UZpYwxyKqqcOfSeUCu4bonj4NfUHCzhxtAAadTKefP4oINDyE8Hji5HDIzHi7YcBD8W6yVMYbC7PvK62f3b91ASf4jtfM4tfFSKXnl4PL0IRaiHYwx3BPde3zvWV4i7grvqh1XPRmkenZj99bdYWthq4cWk6Yw9muY+lKzxJ7G+witbbRWco9mUTZCY270fryOVDnci0vgn52PpDatketoB3NLS7Tt1h0P76aqLUvC8Xhw9emAtl27KWc42jg0feo2abgKaQWS8pOUvbPER4kolqivWdfOoZ1K76yDYwed35hMtKNmJQ4HSwcceuEQ9d7q6fa50xpWAwHGRM5vUMF0mkVpYFTXkWLIdbRDruPj6dzSykpkJCqWxTG3sISHX2flDEfPTl0UxWqJzj0se6hyI/WtgluQMvXJIAGtA5RT9ZtjMggxXHQNs/GqVwNRDlX+OzSpzdVAKOB0oCDrnso6UrUZNfNjdAoeCDPzllN41VBI5VIkFyUrwuzf3llOmfpNstWTQXq59FJOBrEwo/9fLQldw2ycJ1cD4WtxaLIaBZwOOHu1Q8e+wbUWa61e16zroDA9tM74NLUaOaCYDHL10VVl7+x6/nWNk0E6t+qscv3M07ZhS/wQQh6zEThi2LszlauBaHsNPboG1whNvQb35IKJfFsbtWKtRLPGVCNnjCFDlKEMsyt5V5AmTFM7zt7CHj1ceyh7ZzQZhBDDQ9fgDJCNwBFD3/lAw0VWpnFFZaKOMYbl55crazCWV5VjxfkVajfYVkgrcCP/hkoPTdNkEB8HH5XJIO0d29NkEEJMCAWcDlVfZK0eqqwemtTmRVZTEpsRiz8z/1T+LGMynMg8gQO3D0BgJcDVPEXvLLkwWW0yCN+MD3/nGpVBXHvCyaqRy0wTQowCBZwOPXmR1dLaWusXWU1FQUUBouOjwYFTq+G34sIKteNdrF3Qy7WXckJIF6cuNBmEkBaGAq4Baq7o3Vi6vshq7CqkFbj+6DpWXliJkqqSWo+zt7DHmA5jlNfPPGw9aDIIIS0cTTJphEat6E3qpVhcjCt5V3A57zIu511u0KrUB8cd1ErBVkKI4aBJJsQoMMaQU5aDSw8v4XLeZVx5eEVjqavWVq3B4/HwqPyRxjW8qksmUbgRQmqigCM6I2dy3Cm+g8sPFb2zyw8v42H5Q7XjfAW+6O3aG73deiPQNRBt7dqiUFzYoBWpCSGEAo5oTaWsEkkFSbj08BKu5F3BlbwrKKlUvY5mzpmjq3NXBLoGKgNN0+xGquZOCGkoCjjSbEoqS5CYl6jsnd3Iv4FKeaXKMdbm1ujp0lPZQ+veujtsLGzqdX6q5k4IaQgKONJoD8se4kreFWUPLaUoRe0amZOVE3q7Knpmfdz6oJNTJ1jwGjddn1akJoQ0BAUcqRfGGNJF6bj88LIy1LJLs9WO87L3UvbOerv2RjuHds06XZ+quRNC6otuE2iElnCbQJW8CrcLbiuHG6/kXUGRpEjlmOpixNXXznq79oaLjYueWkwIaQnoNgHSYOVV5bj66Kpyuv61/Gtq1fX5Znx0b91d2Tvr6dITdpZ2tZyREEL0iwKuhSqoKFC5fna78DZkTLVCi4Olg8p0fX9nfyp3RQgxGhRwLQBjDFklWSrDjRmiDLXjPGw9lL2z3q69qbo+IcSoUcCZIJlchpSiFGWgXc67jPyKfLXjOjp2RB+3PsrrZx52HnpoLSGEaAcFnAkQS8W4nn9d2TtLfJSIsqoylWPMeeYIcA5Q9tB6ufaCgC/QU4sJIUT7KOD04GjGUeU093Cf8AY/XygRKgoS/9s7SypIUitIbGdhh56uPdHHVdFDC2gdACtzq+Z6C4QQYvAo4HSsoKIA0eeiUVJVgmXxyxDkFlTnvVwPSh+oXD+7U3xH7RgXa5fH18/cesPP0Q9mPDNtvQ1CCDF4FHA6xBjD8vPLUS4tB6CYmr/i/ApsDNuoPEbO5LhbfBeXH17GpTzFDMfcsly1c/k4+Dy+fubWG23t2tL6Z4QQUgMFnA7FZsTiz8w/lT/LmAwnMk/g66tfw5xnrixILKoUqTzPjDNDV6euyh5aoJvmgsSEEEIeo4DTkYKKAkTHR4MDp1avcUviFpWfrc2t0cOlh+L6mVsgerTuUe+CxIQQQhQo4HSg5tCkpgU7AcU1tCn+U9DbrTc6O3VudEFiQgghChRwOnCn+I7K0KQmjyoeob9nf1qVmhBCmgmVqdCBjo4dMcR7CMw4zbMazTgzDPUeSuFGCCHNiAJOB6rXMbMxtwEH1ZmOHDha14wQQrSAAk5HnK2dsSR4ido1OAaGxcGLaV0zQghpZhRwOhTuE64yVFk9NDnCZ4SeW0YIIaanxQZcVlYWQkND0a1bN/To0QM//fST1l+z5lAlABqaJIQQLWqxAWdubo5Nmzbh5s2bOHHiBGbPno2ysrK6n9hEztbOWNJ/CZytFEOWNDRJCCHa0WJvE/Dw8ICHh2J5GFdXVzg5OaGwsBC2trZaf+0RPiNoWJIQQrTMYHtwp0+fxtixY+Hp6QmO4/Drr7+qHbN161b4+vrCysoKffr0wZkzZxr1WhcvXoRcLoeXl1dTm00IIcRAGGwPrqysDD179sTUqVPx0ksvqe0/cOAAIiMjsXXrVgwYMABff/01Ro4ciZs3b8Lb2xsA0KdPH0gkErXnHjt2DJ6engCAgoICvPXWW/jmm29qbYtEIlE5j1AoBACIRKLankIIIUQLqj93GdNcFUoFMwIA2MGDB1W2PfPMM+y9995T2dalSxf26aef1vu8YrGYDRo0iO3Zs+epxy1dupQBoAc96EEPehjIIysrq87PeIPtwT1NZWUlLl26hE8//VRl+/Dhw3Hu3Ll6nYMxhilTpmDw4MF48803n3rsggULMGfOHOXPcrkchYWFcHZ2btQSNSKRCF5eXsjKyoKDg0ODn9/S0e+vaej31zT0+2uapv7+GGMoKSlRjsI9jVEGXH5+PmQyGdzc3FS2u7m5ITdXfe00Tf7++28cOHAAPXr0UF7f+/7779G9e3e1Y/l8Pvh8vso2R0fHRrb+MQcHB/oH0gT0+2sa+v01Df3+mqYpvz+BQFCv44wy4Ko92XtijNW7RzVw4EDI5XJtNIsQQogBMNhZlE/TunVrmJmZqfXW8vLy1Hp1hBBCWiajDDhLS0v06dMHx48fV9l+/Phx9O/fX0+tqj8+n4+lS5eqDXuS+qHfX9PQ769p6PfXNLr8/XH/zlI0OKWlpbhz5w4AIDAwEBs2bEBYWBicnJzg7e2NAwcO4M0338T27dsRHByMmJgY7NixA0lJSWjXrp2eW08IIUTfDDbg4uLiEBYWprZ98uTJ2L17NwDFjd7r1q1DTk4OAgICsHHjRjz33HM6bikhhBBDZLABRwghhDSFUV6DI4QQQupCAUcIIcQkUcARQggxSRRwOrR69Wr07dsX9vb2cHV1xQsvvIDk5GR9N8torV69GhzHITIyUt9NMRrZ2dl444034OzsDBsbG/Tq1QuXLl3Sd7OMglQqxaJFi+Dr6wtra2u0b98e0dHRVDCiFnWtCMMYQ1RUFDw9PWFtbY3Q0FAkJSU1axso4HTo1KlT+OCDD3D+/HkcP34cUqkUw4cP18lCq6YmISEBMTEx6NGjh76bYjSKioowYMAAWFhY4MiRI7h58ybWr1/fLGXnWoK1a9di+/bt2LJlC27duoV169bh888/x5dffqnvphmk6hVhtmzZonH/unXrsGHDBmzZsgUJCQlwd3fHsGHDUFJS0nyNqHfpfdLs8vLyGAB26tQpfTfFqJSUlDA/Pz92/PhxFhISwmbNmqXvJhmF+fPns4EDB+q7GUZr9OjRLCIiQmXb+PHj2RtvvKGnFhkPQHVFGLlcztzd3dmaNWuU28RiMRMIBGz79u3N9rrUg9Oj6nXlnJyc9NwS4/LBBx9g9OjRGDp0qL6bYlQOHTqEoKAgvPLKK3B1dUVgYCB27Nih72YZjYEDB+LPP/9ESkoKAODq1as4e/YsRo0apeeWGZ/09HTk5uZi+PDhym18Ph8hISH1XhGmPoy62LIxY4xhzpw5GDhwIAICAvTdHKOxf/9+XL58GQkJCfpuitFJS0vDtm3bMGfOHHz22Wf4559/8NFHH4HP5+Ott97Sd/MM3vz58yEUCtGlSxeYmZlBJpNh5cqVmDhxor6bZnSq6whrWhHm3r17zfY6FHB6MnPmTFy7dg1nz57Vd1OMRlZWFmbNmoVjx47ByspK380xOnK5HEFBQVi1ahUARQm8pKQkbNu2jQKuHg4cOIAffvgBe/fuhb+/PxITExEZGQlPT09MnjxZ380zSk1ZEaY+KOD04MMPP8ShQ4dw+vRptG3bVt/NMRqXLl1CXl4e+vTpo9wmk8lw+vRpbNmyBRKJBGZmZnpsoWHz8PBAt27dVLZ17doVP//8s55aZFw++eQTfPrpp3jttdcAAN27d8e9e/ewevVqCrgGcnd3B6DoyXl4eCi3N/eKMHQNTocYY5g5cyZ++eUXnDx5Er6+vvpuklEZMmQIrl+/jsTEROUjKCgIr7/+OhITEync6jBgwAC121JSUlKoOHk9lZeXg8dT/cg0MzOj2wQawdfXF+7u7iorwlRWVuLUqVPNuiIM9eB06IMPPsDevXvx22+/wd7eXjkOLRAIYG1trefWGT57e3u165W2trZwdnam65j1MHv2bPTv3x+rVq3Cq6++in/++QcxMTGIiYnRd9OMwtixY7Fy5Up4e3vD398fV65cwYYNGxAREaHvphmkmivCAIqJJYmJicoVYSIjI7Fq1Sr4+fnBz88Pq1atgo2NDSZNmtR8jWi2+ZikTgA0Pr799lt9N81o0W0CDXP48GEWEBDA+Hw+69KlC4uJidF3k4yGSCRis2bNYt7e3szKyoq1b9+eLVy4kEkkEn03zSD99ddfGj/vJk+ezBhT3CqwdOlS5u7uzvh8PnvuuefY9evXm7UNtJoAIYQQk0TX4AghhJgkCjhCCCEmiQKOEEKISaKAI4QQYpIo4AghhJgkCjhCCCEmiQKOEEKISaKAI4QQYpIo4AghhJgkCjhCCCEmiQKOEEKISaKAI4QQYpIo4AghhJgkCjhCCCEmiQKOEEKISaKAI4QQYpIo4AghhJgkc303wBjJ5XI8ePAA9vb24DhO380hhJAWgzGGkpISeHp6gsd7eh+NAq4RHjx4AC8vL303gxBCWqysrCy0bdv2qceYZMBlZ2dj/vz5OHLkCCoqKtCpUyfs3LkTffr0AaD4BrBs2TLExMSgqKgIzz77LL766iv4+/vX6/z29vYAFL9gBwcHrb0PQgghqkQiEby8vJSfw09jcgFXVFSEAQMGICwsDEeOHIGrqyvu3r0LR0dH5THr1q3Dhg0bsHv3bnTq1AkrVqzAsGHDkJycXK9fWvWwpIODAwUcIYToQX0uD3GMMaaDtujMp59+ir///htnzpzRuJ8xBk9PT0RGRmL+/PkAAIlEAjc3N6xduxbTp09Xe45EIoFEIlH+XP0NQigUUsARQogOiUQiCASCen3+mtwsykOHDiEoKAivvPIKXF1dERgYiB07dij3p6enIzc3F8OHD1du4/P5CAkJwblz5zSec/Xq1RAIBMoHXX8jhBDDZ3IBl5aWhm3btsHPzw+xsbF477338NFHH2HPnj0AgNzcXACAm5ubyvPc3NyU+560YMECCIVC5SMrK0u7b4IQQkzU0YyjCD0QitiMWK2/lsldg5PL5QgKCsKqVasAAIGBgUhKSsK2bdvw1ltvKY97cvyWMVbrmC6fzwefz9deowkhpAUoqChA9LlolFSVYFn8MgS5BcHZ2llrr2dyAefh4YFu3bqpbOvatSt+/vlnAIC7uzsARU/Ow8NDeUxeXp5ar665yGQyVFVVaeXchDSEhYUFzMzM9N0M0gIxxrD8/HKUS8sBAOVV5VhxfgU2hm3U2muaXMANGDAAycnJKttSUlLQrl07AICvry/c3d1x/PhxBAYGAgAqKytx6tQprF27tlnbwhhDbm4uhEIhTGwuDzFSHMdBIBDA3d2dihQQnYrNiMWfmX8qf5YxGU5knsDRjKMY4TNCK69pcgE3e/Zs9O/fH6tWrcKrr76Kf/75BzExMYiJiQGg+AceGRmJVatWwc/PD35+fli1ahVsbGwwadKkZm2LUChEcXExXFxcYGtrSx8oRK8YYygrK8OjR49gbW2tcusMIdpUUFGA6PhocODA8PjLPgcO0fHR6OvWVytDlSYXcH379sXBgwexYMECREdHw9fXF5s2bcLrr7+uPGbevHmoqKjAjBkzlDd6Hzt2rF73wNUXYwx5eXlwcHBA69atm+28hDSFtbU1JBIJ8vLyIBAI6EsX0bqaQ5M1ww0AGJhWhypN7j44XajPfRhSqRSpqalo27ZtswYnIU1VUlKC+/fvw8/PD+bmJvcdlxiY1KJUjD80vs7jDo47iI6tOtZ5XIu+D85QSKVSAKAPEGJwqv9OVv8dJUSbOjp2xDPuz9S634wzw1DvofUKt4aigNMyGgIihob+ThJdSilKwa3CWxr3ceBga2GLRf0WaeW1KeAIIYRoRXJhMqYdm4aSyhJ42atXgGJgWBy8WGv3wlHAGSFdVgIghJDGSC5MxtvH3oZQIkSAcwD2jd6HId5DYMYp7sOsHprU1i0CAAWc0amuBFAgLsCy+GUoqCjQSzt2794NjuNgZWWFe/fuqe0PDQ1FQEAAACAgIABdu3ZVO+bgwYPgOA7BwcFq+77//ntwHIdDhw6pvN7Fixeb+Z0QQppbdbgVS4oR4ByAr4d/DQFfgMX9FsPG3AYAtDo0WY0CzojUVglAnyQSCRYtevpf0rCwMNy+fVut1mdcXBxsbW1x8eJFlJSUqO3j8Xh47rnnmr3NhBDt0RRuDpaK2Y7O1s5Y0n8JnK2csSR4iVbLdAEUcEaluhKAjMkAqFYC0JcRI0Zg7969uHr1aq3HhIWFAVCEVk1xcXF4++23wXEczp49q7YvMDCQbkYmxIjUDDd/Z3+VcKs2wmcE4ibEIdwnXOvtoYDTIcYUNzU25nG/5D6WxS8DB9UZcBw4RJ+Lxv2S+406b1Nvg5w3bx6cnZ2Va+tpEhoaCo7jVAKuoKAA169fx+jRo9GnTx/89ddfyn1ZWVlIS0tTBiMhxPAlFybjnWPvKMMtZniMWrgBQOrFh/h23lncuZSn9TbRTVo6VCGtwLN7n23WczIwlFSVYOQvIxv1/AuTLsDGwqbRr29vb49FixZh1qxZOHnyJAYPHqx2jJOTE3r06KESYqdOnYKZmRn69++PkJAQnDx5Urmv+jgKOEKMQ3W4FUmKnhpu5aJKxP1fMiorpIj7v9vw9HOEjYOl1tpFPTjSZO+99x7at2+P+fPn19ojDAsLQ0pKCnJycgAohiCDgoJga2uLkJAQXL58GSKRSLnPzMwMgwYN0tl7IIQ0TkpRikq4fT1MfVgSUIxgndp7G1USRYGBSrEUp/Ylqx3XnKgHp0PW5ta4MOlCg5/HGMOnZz7FmewzyutvNfE4HkLahmDNoDWNalNTWVpaYsWKFZg0aRJ+/PFHTJgwQe2YsLAwbNq0CXFxcZg4cSLi4uIwevRoAMDAgQMBAKdPn8aYMWOU4UclzggxbClFKXg79m0USYrQzbkbvh6mmC2pyZ1LeUhLzFf+zORA2pVHSL34EH5B2lmqjHpwOsRxHGwsbBr8sLW0RVT/KNiY22i8BmdnYYelwUsbde7mqmrx2muvoXfv3li4cKHGte9CQkLA4/Hw119/oaCgADdu3EBISAgAxTBnYGAg4uLikJmZifT0dBqeJMTAPRluMcNiag236qFJTU7tTUa5qFIrbaSAMxLO1opptZqqcWuzEkB9cRyHtWvX4u7du8qliWoSCATKEKu+BWDAgAHK/SEhIfjrr7/o+hshRiC1KLXe4fbk0OSTtDlUSQFnRMJ9wnVeCaAhhg4dimHDhiE6OhqlpaVq+8PCwpCamoq9e/eiT58+KkOQISEhSExMxK+//goLCwuV8COEGI7UolRMi51Wr3ADgMIHZUhLzAeTa95fPVRZ8ED9MxxKJJ0AACAASURBVKOpKOCMCMdxOq8E0FBr167Fo0ePcOnSJbV91b2ygwcPKocnq1VPKPntt9/wzDPPwNbWVuP5qVAwIfqTWpSKt48pem5dnbrWGW4A4ORpC4+Otd/PyvGA9oEucPa0a+7mUsAZG11XAmiowMBATJw4UeO+QYMGwdzcHIwxtYBzdHREjx49wBhDaGio2nPLyxXVW/h8frO3mRBSt+pwKxQXoqtTV+wYvqPOcAOAezcKkJchrHW/pZU5QiZ2bs6mKtGCp41QnwX3xGIx0tPT4evrCysrKx230PTMmjULW7ZsQXFxMc2ubCL6u0kaqrHhlnwhF39+dwtMztDayw75WerDkMPf9m/QLMqGLHhKtwkQg3bp0iUkJCRg165dGDduHIUbITp2p+hOo8Lt6sksnP0xFQDQ6Vk3hL3ZBce/SUL6NcX1OI4H+PZ00dotAgAFHDFwL7/8MoRCIcaNG4fNmzfruzmEtCh3iu5g2rFpDQo3xhj++V86Lv6eAQDoMbgtBr7sB47HIWRSF9xPPo/KCqlWhyarUcARg5aenq7vJhDSIjUq3OQMpw+k4MapbADAs+N80Wekj3JymI2DJUJf74yzP6Zi0IROWi3TBVDAEUIIeUJjwk0mlePP3TeRejEP4ICQ1zohIKSt2nF+QW5aHZasiQKOEEKI0t3iuw0OtyqJDEdjriMzqRA8Mw5Dp3bTWYg9DQUcIYQQAIpwi4iNQKG4EF2cutQr3MRlVfj9q6vITRPB3JKHkdO7w9vfMG5fooAjhBCiHm7D6g63smIJDm1OROGDMvBtzDFmZk+4t697hqWuUMARQkgLd7f4LqbFTlMJN0er2quPAEBxXjkOb06EKF8MG4Elxn3UC85tmr8aSVNQwBFCSAuWVpyGabHTUCAuQOdWnesVbvn3S3Bo81VUiCohcLHGuFm94NC66UtvNTeTK9UVFRUFjuNUHu7u7sr9jDFERUXB09MT1tbWCA0NRVJSkh5bTAgh+pFWnIaI2AhluH0z/Js6w+1BajEOrr+CClElnNvaYfwnfQwy3AATDDgA8Pf3R05OjvJx/fp15b5169Zhw4YN2LJlCxISEuDu7o5hw4ahpKREjy1umNSLD/HtvLO4cylPb23YvXu38gtEXFyc2n7GGDp27AiO41RqS3Ich5kzZyp/zsjIUJ5n//79auep/sKSn/94ocQpU6aofYmp+SCE1K0x4ZZxLR+HNieiskIKj44CvPhxb63fy9YUJjlEaW5urtJrq8YYw6ZNm7Bw4UKMHz8eAPDdd9/Bzc0Ne/fuxfTp0zWeTyKRQCKRKH8WiUTaaXg9VC8cWFkhRdz/3Yann6Ne/4LZ29tj586dagWST506hbt37zaotNbChQvx0ksvwcLCos5jra2tcfLkyYY2lxAC9XDbMbzuYcnk8zn4c89tMDmDT4/WCH/bH+aWZjpqceOYZA8uNTUVnp6e8PX1xWuvvYa0tDQAiqoYubm5GD58uPJYPp+PkJAQnDt3rtbzrV69GgKBQPnw8vLS+nvQ5MmFA7W5UGB9TZgwAT///LNa6O/cuRPBwcHw9vau13lGjhyJtLQ0bN++vV7H83g89OvXT+ODEFK7NOHjcOvUqhN2DN+BVlatnvqcq39m4cRuRdHkzs+6Y8T0AIMPN8AEA+7ZZ5/Fnj17EBsbix07diA3Nxf9+/dHQUEBcnNzAQBubqo3ILq5uSn3abJgwQIIhULlIysrS6vvoTZ3LuWpLBxYvVBg6sWHemkPAOXSOPv27VNuEwqF+PnnnxEREVHv8wwePBjh4eFYvny5UQ0XE2JM0oRpiDj6ONy+Gf7NU8ONMYYLh9Jw9idF0eSeg70wZHJXmJkZR3QYRysbYOTIkXjppZfQvXt3DB06FL///jsAxVBktSev0zDGnnrths/nw8HBQeXRGIwxVElkjXqI8isQ93+3NZ437v+SIcqvaNR5m7pakoODA15++WXs2rVLuW3fvn3g8XiYMGFCg861du1a5Ofn4/PPP6/X8VKpVO0hl9eybDAhLVya8PFsyfqEm1zOcHpfCi7+kQEAeHZcewx4pSM4nvFc5zbJa3A12draonv37khNTcULL7wAAMjNzYWHh4fymLy8PLVenTZIK+WImXWq2c9bWSHF94viG/Xcd/8TAgt+04YaIiIiEBYWhqSkJPj7+2PXrl145ZVXGry0Tc+ePTFp0iRs2LABM2bM0HgdtVpZWZnGa3VDhgzBiRMnGvweCDFl1eGWX5Ffr3CTSeU4sfsm7lTXlZzYGQHPtdFhi5uHyfXgniSRSHDr1i14eHjA19cX7u7uOH78uHJ/ZWUlTp06hf79++uxlcYtJCQEHTp0wK5du3D9+nUkJCQ0aHiyphUrVqCqqgrLli176nHW1tZISEhQe2zdurVRr0uIqUoXpivDza+VX53hViWR4Y+t13DnYh54ZhyGT/M3ynADTLAHN3fuXIwdOxbe3t7Iy8vDihUrIBKJMHnyZHAch8jISKxatQp+fn7w8/PDqlWrYGNjg0mTJmm9beaWPLz7n5AGP48xhuO7knDvRoHy+ltNHA/w6d4awyL8G9WmpuI4DlOnTsXmzZshFovRqVMnDBo0qFHn8vHxwYwZM7BlyxbMmTOn1uN4PB6CgoIa22RCWoR0YToiYiOU4bZz+M6nhpu4rAr/23IVD9MNr65kY5hcD+7+/fuYOHEiOnfujPHjx8PS0hLnz59Hu3btAADz5s1DZGQkZsyYgaCgIGRnZ+PYsWM6WSma4zhY8M0a/LC0MkfYG11hwdf8fcTSyhyhr3dp1Lmb676xKVOmID8/H9u3b8fUqVObdK5FixbBxsYGn332WbO0jZCW6Mlwq6vnVlokwcH1l/EwXQS+jTmejww06nADTLAHp+lm4Zo4jkNUVBSioqJ006BmUr1Q4LFv1KuuhEzqrPebLdu0aYNPPvkEt2/fxuTJk5t0LmdnZ8yfPx8LFy5EWVlZM7WQkJZD07Ckk5VTrccXPyzHoc2JKCkQw1ZgibGzesHZ07DqSjaGyQWcKevYxxV3Lj5E+jXFrQIcD/Dt6WIQ6y4BwJo1a5rtXJGRkfjqq69w5MgRjfvlcjnOnz+vcV9gYCD4fH6ztYUQY1Idbo8qHqGjY8c6w+1RZgkOf5mIipIqg64r2RgUcEaE4ziETOqC+8nnUVkhhaWVOUImdtZ3s7TCxsYGUVFRePfddzXur6ioQHBwsMZ9qamp6NixozabR4hByhBmqITbzvCdTw23B6lF+P2ra6gUy9Dayw5jP+yl99Gg5sSxpt4I1QKJRCIIBAIIhcJa74kTi8VIT0+Hr68vrKysmvX1Uy8+xNkfUzFoQid07OParOcmpk+bfzeJ/mQIMxARG1HvcEu/lo/YHTcgq5LD088Ro2b0AN/a8Ps89fn8rWb474ao8QtyM5hhSUKI/j0ZbnUNS94+n4OTRlZXsjEo4AghxIg9OSz5zfBv4Gxd++zHq39mKUtvde7njsFvdgHPSEpvNRQFHCGEGKl7onuYFjsNeRV5dYYbYwz/HE5Xlt7qOcQLA14yrtJbDUUBRwghRuie6B4ijkbUK9zkcobT+1OQdDobAPDs8+3RZ0Q7k18/0TT7pYQQYsJqhlsHQYenhptMKsfxnUmKcOMU980GjfTRW7glx5/BtnffQHL8Wa2/FgUcIYQYkXuie4iIfRxuO8N31hpulWIpft96DXcuKepKhr8doNe6kuXCYhyP2aL4c8eXKBcWa/X1KOAIIcRIKMOtvO5wE5dW4dB/EpF1sxDmljyM/qCHXm8rYozh+I6vUCmuAABUVlTgxDfaLY5OAUcIIUYgU5SpEm7fhNc+LFlaJMEv1XUlbf+tK9lNv3Ulk+PP4E5CPNi/azYyuRyp/5xDcvwZrb0mBRwhhBi4TFEmpsZOVQm31tatNR5b/LAcv3x+CUU5ZbAVWOLFj3vDvb1Axy1WVS4sxokdXwF48rofh+M7tmhtqJICjhBCDFjNcGsvaP/UcHuUWYJfvriEkkIxBK7WGP9JH70XTVYdmnyycBbT6lAlBZwR0uUsJEKI/tQclmwvaI+d4TtrDbcHqUX4dcNlVJRUobWXHcbP7WMQRZMLsu6pDE0+qXqoMj/rXrO/NgWckdH1LKTa7N69GxzH4eLFiw1+bkZGBkaPHg0nJyflIrQNwXEcOI7TuHrB09p15swZvPrqq2jTpg0sLS0hEAjQv39/bNu2TWVZHh8fH+VrPPkIDQ1t8PslpDGyRFmIiI3Aw/KHdYZb+tVHOLT5KirFMnj6OeKFOb0Npmiys1c7+AbWvjgxx+PB75n+aO3Vrtlfm270NiK1zUIa97FxLQw6e/ZsXLhwAbt27YK7uzs8PDwadZ41a9bg3XffhZNT7TX3qi1duhTR0dHo378/li9fjg4dOqC8vBznzp1DVFQUUlJSsHHjRuXxAwYMwBdffKF2nrqKuxLSHLJEWZgaO7Ve4aZWV/Idf5hbGE5dyaKcB0/pnXGwtLbG0LdnaOW1KeCMSPUspGo1ZyF1Dh6kx5Y1zI0bN/DMM8/ghRdeaPQ5hg4diri4OKxcuRLr169/6rE//fQToqOjMW3aNOzYsUPlBteRI0di3rx5iI+PV3mOo6Mj+vXr1+j2EdJYNcPNV+D71HBLPJGJv/97BwDQpZ87wgysrmR28i38+vlyiEtEsBYIUCEUPnEEw7B3ZsJG4KiV1zec30QLwBhDlVjcqIcw7yGO79gCjbOQYrZAmPewUedtrtWSpkyZAjs7O9y5cwejRo2CnZ0dvLy88PHHH0MikQAA4uLiwHEc7ty5gyNHjiiH/TIyMgAAmZmZeOONN+Dq6go+n4+uXbti/fr1kGsYu+/cuTOmTZuGr776CvfuPX3sPjo6Gq1atcLmzZs1Vm+wt7fH8OHDm/5LIKSJngy3XeG7NIYbYwznf72rDLeeQ70w+K2uBhVuqRfO4b/LF0JcIoJ7x06YvG4LOvYNBsdTtLF6aFKbX86pB6dDUokEmye/3MxnZZCUl+GbD6c16tkfffdfWDTTmmBVVVUYN24cpk2bho8//hinT5/G8uXLIRAIsGTJEvTu3Rvx8fF48cUX0aFDB+UQoIeHBx49eoT+/fujsrISy5cvh4+PD/73v/9h7ty5uHv3LrZuVZ9lFRUVhe+//x6LFy/Gnj17NLYpJycHN27cwIQJE2BjY1Pv98IYg1QqVdtuZmZm8vX7iH5kibIQcSyiznCTyxlO70tG0pkHAIB+L7RH73DDqit5+Y/f8NeebwDG0L7PMxjz0TxYWFlh2DsfICvpGiTlZVodmqxGAUeaTWVlJZYtW4ZXXnkFADBkyBBcvHgRe/fuxZIlS+Dg4IB+/fqBz+erDQFu2LAB2dnZuHDhAp555hkAQHh4OGQyGbZv347IyEh06tRJ5fXc3d0xe/ZsrF69GnPnzkWPHj3U2pSZmQkA8PX1bdB7+eOPP2BhYaG2ffny5Vi0aFGDzkVIXbJKFOGWW5arGJYcrnlYUlYlx/Fvb+Lu5TyAA0IndYb/IP2V3noSk8tx6oeduPT7bwCAnsNGYXDEdPB4imuCNgJHDHt3Jk5++zUGT31Pa0OT1SjgdMicz8dH3/23wc9jjOGPLz9H2pWLGqfacjwe2gf2xeiPPmlUm5oLx3EYO3asyrYePXrg5MmTdT735MmT6NatmzLcqk2ZMgXbtm3DyZMn1QIOAObNm4evv/4a8+fPx5EjR5r2BmoYOHCgyqSTam3aGM6HCTENWSWK2ZI1w83FxkXtuEqxFEe/vo6sW0XgmXEYFuGv19JbT5JWVuLIVxuQcl5x+9KgSVPQd9xLaj3LzsGDdDZngAJOhziOa/Rw4PDpH2FX5HRIysuherOkYhbS8OkfNttQY2PZ2NjA6ok28Pl8iMXiOp9bUFAAHx8fte2enp7K/Zo4ODhg0aJFiIyMxF9//aW239vbGwCQnp5eZxtqEggECAqqfWozIc2hZrj5OPjUGm7i0ioc3nIVeRkimPPNMGp6d3h1q3v2sK5UlJbgt8+XI/v2TfDMzDFiRiS6DgzVd7NokomxsBE4Yug7H0BTJQBtzkLSFWdnZ+Tk5Khtf/BAcZ2hdWvNs8gA4P3334evry/mz5+vNmnGw8MD3bt3x7Fjx1BeXt68jSakCZ4Mt13huzSGW3VdybwMRV3JFyIDDSrchHm52Lf4E2Tfvgm+jS1eXhhtEOEGUMAZlc7Bg3Q+C0lXhgwZgps3b+Ly5csq2/fs2QOO4xAWFlbrcy0tLbFixQokJCTgp59+Utu/ePFiFBUV4aOPPtI4a7S0tBTHjh1r+psgpJ6ySrIwLXZaneGmUlfSkY/xH/eBm6/h3IuZezcVexfNRdGD+7B3dsFr0evg5a9+LVxfaIjSiHAcp/NZSLoye/Zs7NmzB6NHj0Z0dDTatWuH33//HVu3bsX777+v8fpbTRMnTsQXX3yh8TrcK6+8gsWLF2P58uW4ffs2pk2bprzR+8KFC/j6668xYcIElVsFiouLcf78ebVz8fl8BAYGNv0Nkxbrfsl9TIudhpyynKeG26PMEhz+MhEVJVVwdLPB2I96wsFZ/6W3qqVdScDhjWsglUjg0s4X4z+Ngp2TflcseBIFnJHR9SwkXXFxccG5c+ewYMECLFiwACKRCO3bt8e6deswZ86cOp/PcRzWrl1b6/1s0dHRGDp0KL788kssXLgQ+fn5sLa2hr+/P+bMmYPp06erHP/3338jODhY7Txt2rTB/fv3G/cmSYt3v+Q+ImIj6gy37OQi/L7tGqrEMrh422PMzJ4GU3oLAK79eRQnvtkKJpejXY9AjJ29APwG3IajKxxrrjt9DdTq1avx2WefYdasWdi0aRMAQCKRYO7cudi3bx8qKiowZMgQbN26FW3btq3XOUUiEQQCAYRCYa2lm8RiMdLT0+Hr66s28YIQfaK/m/rxZLjtDN8JVxv1WZBpiY9w7JskyKRyePo5YvSMHrC0Noy+CGMMfx/4ARcOHgAA+IcMxbB3Z8LMXHftq8/nbzWTvgaXkJCAmJgYtfujIiMjcfDgQezfvx9nz55FaWkpxowZA5lMpqeWEkJMWXZptsqwZG3hdutcDo7G3IBMKodvz9YY+1FPgwk3mbQKR7/aoAy34JcnIvz9WToNt4Yy2YArLS3F66+/jh07dqBVq1bK7UKhEDt37sT69esxdOhQBAYG4ocffsD169dx4sQJPbaYEGKKskuzEXE0Ag/KHjw13BJPZOLknltgcoYuwe4Y8W6AwRRNlpSX4ZfVUbh55i9wPB6GT/8I/V953aCqp2hisgH3wQcfYPTo0Rg6dKjK9kuXLqGqqkrlWo2npycCAgJw7tw5jeeSSCQQiUQqD0IIqUvNcGvn0E5juDHGEF+jrmQvA6srWVKQj/1L5yPzxlVYWFnjxflL0X2wcdRuNdy+ZRPs378fly9fRkJCgtq+3NxcWFpaqvTqAMDNzQ25ubkaz7d69WosW7ZMK20lhJimJ8NtV/gutXCTyxlO7U3GzbOGWVfyUWYGflm9FKWFBbB1bIUXP42Cm28HfTer3gzjK0IzysrKwqxZs/DDDz806AI6Y6zWv1QLFiyAUChUPrKyspqruYQQE6TWcxuu3nOTVclx7JsbuHn2ATgOCH29M/qM8DGYcLt3PRH7l8xDaWEBnNp4YdKK9UYVboAJ9uAuXbqEvLw89OnTR7lNJpPh9OnT2LJlC2JjY1FZWYmioiKVXlxeXh769++v8Zx8Ph/8ZqzZSAgxXdUTSmqGm5utm8oxlWIpjmy/jvu3i8Az5zBsqmHVlbx5+iRit2+GXCZF264BeH7uIljZ2em7WQ1mcgE3ZMgQXL9+XWXb1KlT0aVLF8yfPx9eXl6wsLDA8ePH8eqrrwJ4vKTKunXr9NFkQoiJeFD6ANNipyG7NLvWcFOrK/led3h1NYzSW4wx/PPrTzi7X7H8VOfgQRjxwRyYa1hZwxiYXMDZ29sjICBAZZutrS2cnZ2V26vXK3N2doaTkxPmzp2L7t27q01IIYSQ+npQ+gARsRHILs2Gt723xnArLRLj0H8SUZRbDitbC4yZ2dNgSm/JZTL8uXMbrv15FAAQNHY8nps0RVka0BiZXMDVx8aNG2Fubo5XX31VeaP37t27YWZmGFNyCSHG5clw2xW+Sy3cinLLcGhzIkoLJbBrxcfYj3rBycNWTy1WVSmuwO//WYe0ywkAx2Hw1OkIDB+j72Y1mfFGcwPExcUpq5gAgJWVFb788ksUFBSgvLwchw8fhpeXlx5b2EA3fgE+9wOSDuq7JYS0OEczjiL0QChiM2IBaOi5hav33PLuiXBw/WWUFkrg6GaD8Z/0MZhwKysuwo/LFiDtcgLMLSwx7uPPTCLcgBYScCal9BHwv0igLA84PEvxsx7s3r0bHMfh4sWLtR6zadMmjB8/Hr6+vuA4DqGhoU8959GjRzF69Gi4uLiAz+fDy8sLkydPxs2bNxvdzvLyckRFRSEuLk5tX1RUFDiOQ35+vsbnBgQEqLQ5IyMDHMepPBwcHNCzZ09s2rSJKuG0AAUVBYg+F40CcQGWxS/DzfybynDzsvfCzvCdcLd1V3lOdnIRft14BRUlVXDxtsf4ub1h72QYJdIKH9zHvsVz8TDtDqztHfDKklXw66teg9VYUcAZE8aA/80GJKWKnyWlwO91FyLWl+3bt+PevXsYPHgwXFzUC8rWNG/ePIwcORJyuRxbt27F8ePHsXTpUiQkJKB379745ZdfGtWG8vJyLFu2TGPANdaHH36I+Ph4xMfH48cff8SAAQMwe/ZszJs3r9legxgexhiWn1+OcqliXcGyqjJMPjpZGW67wnephVta4iMc/vIqqsQytOnkiBdmB8La3jCKJmffvol9iz+BMO8hHN08MHHFF/Ds1EXfzWpWLfIanNFK+gW4ffjxz0wG3DqkGLIMGK+/dtXi5s2b4P17gfrJiT817du3D59//jnef/99bN26Vbn9ueeew8SJExESEoI333wTvXr1Qvv27bXe7rp4e3ujX79+yp9HjBiBGzduYN++fVi/fr0eW0a0KTYjFn9m/qn8Wc7kEMvEcLZy1hhut849wF/f3wZjgG/P1hj+tr/BlN5KOX8Wf2xZD1lVFTw6dsYL85fAxkGg72Y1O+rB6RJjQGVZ4x5F94DDkQCevAmUUwxZFt1r3Hm1uJgEr56zr1auXIlWrVrhiy++UNtna2uLL7/8EuXl5di4caNye2hoqMYhzylTpsDHxweAYkixuue4bNky5bDilClTGvxe6iIQCGBhpFOpSd0KKgoQHR8NTu3fHyCRSWDBU/1/f+V4Jk7uUYRbl/4eBlVX8tLvv+LwprWQVVWhQ1A/vLJkpUmGG0A9ON2qKgdWeTbzSRkgFgL/aeQqup89ACz1d7E7JycHSUlJmDBhAmxqWU8qODgYrq6uOH78eIPO7eHhgaNHj2LEiBGYNm0a3n77bQCoc7i0LnK5HFKpFICiePdvv/2Go0ePYv78+U06LzFMNYcmGdS/EFZIK7Di/ApsDNsIxhjO/5qGy7H3AAC9hnmj//gOBlGdhMnliPt+Jy7/8RsAoFf4aIRNeRc8nmEErzZQwBG9yszMBAD4+vo+9ThfX19cu3atQefm8/nKijZt27ZVGVZsivnz56uF2ZQpU6heqYm6U3xHZWjySTImw4nME0gtSMX9P6S4+XcOACD4xQ7oHd5OV818qqpKCY5sWY/UC4qC8s+9PhVBY8cbRPBqEwWcLlnYKHpMDcUY8PPbQOoxxXW3J3FmQKdw4KVvGtcmI/C0WqG6NmvWLLzxxhsAFMsyxcfHY8WKFSgrK8OPP/6o59aR5tbGrg3cbd2RW6a5GLsZZ4YwzyG4+18x7l559G9dyS7oNrC5R2sap6JEhF/XLceDlFswMzfHiBmz0WVAiL6bpRMUcLrEcY0fDhz3JbClDyAWASrDJBzAtwfGbtbrUGNjeXt7AwDS09Ofety9e/ea/V5F838Xaqxter9UKtV4Xa1t27YICgpS/hwaGgqO47BgwQLExsYiPDy8WdtJ9CdLlIXZcbNVwq1Dfi8MyHgJZ31/RrrzVQi4VhhwdQLupj4Cz5zD8Ah/dOhtGHUlix/m4pfVS1GUkw2+rS2en7sIXt2667tZOkOTTIyFnQswZiOgdg2AKbbbNe26kr54eHjA398fx44dQ3l5ucZj4uPj8fDhQwwbNky5zcrKChKJRO3Y2u5p08TNTXEzbnZ2tto+xhhycnKUx9SletX4q1ev1vv1iWE7lXUKE/43AclFyXCycsL0HtNhVWWH59ImwLrKHiF3J0BQ7oI30xbiYWopLPhmGDOzp8GEW+6dFOxbPBdFOdmwb+2CidGft6hwAyjgjIv/eKDLWMWQJKD4s+s4g7xFoCEWLlyIoqIizJ07V21fWVkZPvroI9jY2GD27NnK7T4+PkhJSVEJuYKCArVFa6tXgaioqFA79+DBg8FxHA4cOKC27+jRoxCJRPWuT5qYmAgAcHU1jA830ngyuQxfXvkSM0/ORElVCXq49MCBMQcwo+cMvJTzASxkfHDgYCGzwqs35kGSw4OVrQWenx0Iry6GUTT57qV/cCB6AcqFxXDxaY9Jy7+Ac1tvfTdL52iI0phwnKK3lnFaMXOSbw+M3qDXJp08eRIZGRlq20eNGoWbN28q94lEIjDG8N///hcA0LdvX7Rrp7gAP3HiRFy+fBlffPEFMjIyEBERATc3NyQnJ2Pjxo24e/cu9u7dq3IP3Jtvvomvv/4ab7zxBt555x0UFBRg3bp1cHBQLVxrb2+Pdu3a4bfffsOQIUPg5OSE1q1bw8fHBx06dMDMmTPx+eefo7i4GKNGjYK1tTUSEhKwZs0aBAUFYdKkSWrvLTMzE+fPnwegCOD4+HisXr0a7dq1w/jxxv1lo6UrFhdj/pn5OPdA8UXptc6v7olfvQAAIABJREFUYV7febAws0DqxYewz358XY0HHiDjgW9rjhfn9jaY0ltXjx/Bnzu3gTE5fHr2xtjZn8LS2jiutTc7RhpMKBQyAEwoFNZ6TEVFBbt58yarqKho/gZc/5mxdR0Zu/FL85+7nr799lsGxXipxkd6ejqbPHlyrfu//fZbtXP+8ccfbNSoUczZ2ZlZWFiwNm3asDfffJMlJSVpbMN3333HunbtyqysrFi3bt3YgQMH2OTJk1m7du1Ujjtx4gQLDAxkfD6fAWCTJ09W7pPL5Wzbtm0sKCiI2djYMEtLS+bn58fmz5/PSkpKVM6Tnp6u9j6srKxYp06dWGRkJMvJyWnqr1UntPp304jdeHSDDf9pOAvYHcCCvg9ih+4cUu4rE0pYTOQptmX6n2qPmFlxrEwo0WPLFeRyOTu9dzf74tXR7ItXR7Oj2zYxaVWVvpvV7Orz+VuNY0yLd/qaKJFIBIFAAKFQqNZjqCYWi5Geng5fX98GrSxOiLbR3011P6f8jJUXVqJKXgVve29sCN2Azk6dASiuxx79+jrSr+WDydWfy/EA354uGDldf9e3ZNIqxG77D26djQMABL88CcEvTzSYmcfNqT6fv9VoiJIQ0mKJpWKsurAKB+8oVuYI9QrFyoEr4WD5+IOz8EEZ0hJrn7zE5EDalUcoeFAKZ0/dr3otKS/DofUrkXnjGnhmZhj2zkwEhA2r+4ktAAUcIaRFyi7Nxuy/ZuNW4S3wOB4+DPwQEQER4HGqc+/KRBLwzDnIpZoHu6p7cPoIN1H+IxxcE4X8rHuwsLLGuDkL4NOzt87bYago4AghLc7Z7LP49MynEEqEaMVvhbXPrUWwp+oyMTKZHP8cSsPlY5kAU8zx0nRBx9LKHCETO+uo5Y89upeOX9ZEobSwALatnDD+0yi4+ui/GLkh0eltAhEREdi1a5fGfYcOHVJOtX7S0qVLlSWXCCGkseRMjm1Xt2HGiRkQSoQIcA7AgTEH1MKtOK8cv6y7hMuxinDrNsgTgyd31XjOkEmdYeOg2yVw7l1LxP6l81BaWADntt6YtOILCjcNdNqD2717NwBF0D3phRdewJQpUzQGYGZmZq3hRwgh9SGUCLHgzAKcyT4DAHil0yv49JlPYWmmGk7J53Nwal8KqiQy8G3MEfZmF3QIdAVjDOmJj5STTaqHJv2C6lcMoLkknfoTx77eDLlMhrbdAvD8x4tgZaf74VFjQEOUhBCTd6vgFmbHzUZ2aTb4Znws6rcIL3R8QeWYygopTu1LRso/DwEAnn6OGDq1m3L1bY7jEDKpC+4nn0dlhVTnQ5OMMVz45QD+/vEHAECXASEIfz8S5rRMU60o4AghJu3XO79ixfkVkMgkaGPXBpvCNqGLk+rK1blpQhzflQRRvhgcj8MzY3zQe4QPeDzVafY2DpYIfb0zzv6YikETOulsaFIuk+HEN1/h+sljAIC+z7+MQa+9Ba6eay62VBRwhBCTVCmrxOp/VuO/KYrqOYPaDMLqQash4D9e3FMuZ7gcew//HE4HkzPYO1th+DR/uLevfQFQvyA3nQ5LVoor8L+Na5CeeAkcx8PgqdPRK3y0zl7fmFHAEUJMTk5pDubEzcGNghvgwGFGrxl4t8e7KrcAlBaJceLbm8hOKQYA+AW5IuT1LuBbG87HYllxEX5ZE4W89Lswt+Rj9Kx56Bj0rL6bZTQM5/8kIYQ0g/gH8Zh3eh6KJcUQ8AVYM2gNBrYZqHJMWuIjnPz+FiRlUpjzzRDyWid07uduUJU/CrKz8MvqKIgePYS1gwAvzlsCDz/d345gzGgA1wiJjhxBysBBEB09qu+m4Nq1a5g6daqy7JOdnR169+6NdevWobCwEIBivbSAgICnnmfKlCmwe8pMMDs7O0yZMkX5c1xcHDiOU3m0atUKzz77LL777rtmeW/EuMiZHDuu7cB7J95DsaQYXZ264sCYAyrhVlUpQ9zeZBzZfh2SMilcvO0x4bO+6BLsYVDhdv/WDexf/AlEjx7C0d0Dk5Z/QeHWCDrvwX333XcaP4A4jqt1H3lMWlCAnCVLIS8pQc6SpbDp2xfmzs56acuOHTswY8YMdO7cGZ988gm6deuGqqoqXLx4Edu3b0d8fDwOHjyo1TasWrUKYWFhABRrwe3ZswdTpkyBSCTChx9+qNXXJoZDVCnCwjMLEXc/DgAw3m88Pnv2M/DN+MpjCrJLEftNEopyygAAgcO88ezz7WFmbljf85Pjz+LIV+shq6qCh19nvDBvCWwcar8mSGqn84BrbG1nQ/p2pS+MMeRGRUH+78Kg8rIy5EYtQ9svN+u8LfHx8Xj//fcxbNgw/Prrr8p11wBg2LBh+Pjjj3FUBz1MPz8/9OvXT/nzqFGjkJCQgH379lHAtRDJhcmYHTcbWSVZsORZ4rNnP8NLnV5S7meM4XpcNs79fAcyqRw2DpYYOqUbvLoZxtpt1RhjuPT7rzj1/U4AQMe+/TDqw7mw4FNB7MbS6VcXuVze6IdMJqvXa2zbtg09evSAg4MDHBwcEBwcjCNHjij3SyQSfPjhh2jdujVsbW0xbtw43L9/X1tvuVmVHDmCkuMngOrfhUyGkuPHIarx/nRl1apV4DgOMTExKuFWzdLSEuPGjdN5u3g8Huzs7GBB9wa1CIfvHsYbf7yBrJIseNp6Ys+oPSrhVlFSiT+2XsOZAymQSeVo190Zry1+xuDCTS6X4a/vYpTh1it8DMbOWUDh1kQmN8mkbdu2WLNmDTp27AhAMST6/PPP48qVK/D390dkZCQOHz6M/fv3w9nZGR9//DHGjBmDS5cuwczMTKttY4yBaVhZuj6khYXIWbJUvSAexyFnyVJYde8Oc6eG/6PlrK0b3DuWyWQ4efIk+vTpAy8vrwa/ZnOSy+WQSqUAFCt6f/vtt7hx4wZiYmL02i6iXVWyKqxLWIf9yfsBAAM8B2DNoDVwtHJUHpN1qxAndt9EubASZuY89H+pA7qHtjW40aCqSgn+2PwF7iTEAwCeeyMCQWNeNLh2GiOTC7ixY8eq/Lxy5Ups27YN58+fR9u2bbFz5058//33GDp0KADghx9+gJeXF06cOIHw8HCtto1V/D975x0eVbX14fdMTyekh5BCSyW00MHQLSB6RVG5WMEC6pWL+nnRq9guCtgRRRTBhh3sqPQWQAgJoYWaBEISUkhPpp79/THJQEiAgCkjnPd55pmcs8tZk2TO7+y11167mgM9mzinphDI5eUcGXFp22NE7kxGcr243X4LCwupqqoiIiLikq7ZlNx66611jlUqFU8//TT33XdfK1mk0NzkVebx2PrHSCtIA+DBbg/yYPyDqFX2B1SbVWbbj0dJWWnPI+kd6MqoyXH4hjhfOquqslK+n/siuQfTUWs0XPPQdKIGXNXaZl02OJXAmUwmXn/9dX755RcKCwsJDg5m3LhxPPjgg5c0urLZbHzzzTdUVlbSv39/kpOTsVgsjBo1ylEnODiYuLg4kpKSzilwJpMJk8nkOC4rK7v4D6fQLMyePZthw4YBUFJSwurVq3nllVcwmUzMnTu3la1TaGr+zP2TJzY8wSnjKTx0Hrw86GUS2yc6ykvyq1i5aC/5WeUAxA4OZuAtndHqmtc7cymU5OWy7JWZFOfmoHdz48bHnyEk5vzRxgoXR4sK3NKlS5k8eTLTpk1j1qxZdcpMJhNDhw5l27ZtjkCUgwcPsn79en777Td++umnRl9n9+7d9O/fH6PRiLu7O8uXLycmJobU1FR0Oh3e3t516gcEBJCXl3fO/l5++WWef/75i/ikDSO5uBC5M/mi2wkhyHn8CSo2bDg9/3YmajXuiYm0e/Xib+iSi8tFt/H19cXV1ZWMjIyLbnsuNBrNeedZrVZrg/NqHTp0ICEhwXE8YsQIiouLee2115g0aRJRUVH12ij8/RBCsHjvYt7a+RaykIn0juSNoW/Q3qO9o/zA1jw2fFk/SbIzknv4AMtnv0B1WSkevn6Mm/E8PiGhrW3WZUeLCtyaNWswmUxMmjSpXtnrr7/O1q1bUavVTJs2jSFDhnD48GFeeuklfv31V7744gtuv/32Rl0nMjKS1NRUSkpK+O6777jrrrtYv379OesLIc7r754xYwbTp093HJeVlV3S3JMkSRftDqwl6KUXOXLNtcgVFfXm4FRubgS9+AKqS+z7YlGr1QwfPpwVK1aQnZ1NSEjIX+4zICAAo9HIqVOnaHvWXGJRUREmk4mAgMalR4qPj0cIQVpamiJwlwEV5gqe2fwMq46tAmBsx7H8t99/cdHYH85M1VbWLz3Aoe2nkySPvDcGd2/nDNA4kryNn9+cg9Vswj+8I//4z0zcvZ0r6OVyoUWjKLdv305kZCQdO3asV7Zw4UIkSeLJJ59k7ty5jB49mkcffZRff/0VIQSfffZZo6+j0+no1KkTCQkJvPzyy3Tr1o233nqLwMBAzGYzxcXFdern5+ef9+ap1+sdUZm1r5ZG4+ND0PPP1d9xUQiCnn+uxdfCzZgxAyEE9913H2azuV65xWK5qFF37ZzoV199Va/s66+/rlPnQtRureTv75xP7wqN53DxYW7/5XZWHVuFVqXlmX7P8NLAlxzilne0lK//9yeHtp9EUkn0HduBG/7dw2nFLfWPX/lh7v+wmk2Ed+/Frc+9rIhbM9KiI7i8vDyuuqr+BOrRo0fJyspCkiSmTp1ap6x379706NHjL+0HJ4TAZDLRq1cvtFotK1euZPz48QDk5uayZ88e5syZc8n9txQe116Lx4oVlK9Za3dVqtV4DBuG57XXtrgt/fv357333mPq1Kn06tWLKVOmEBsbi8ViISUlhYULFxIXF+cI+ikrK+Pbb7+t14+fnx+JiYkMHTqUsWPH8uijj5KZmUliYiJCCDZs2MAbb7zB2LFjGTJkSL32hw4dYuvWrQCUlpayatUqFi1aREJCAoMHD27W34FC87IiYwUzk2ZSba0m0C2Q1xNfp6tfV6AmSfJvWfz5c+OTJLcmQpbZ9OUn/PmD/TsQN3QUIyZPRa1xqjCIyw/Rgmi1WnHrrbfWO//pp58KSZJETExMg+1uueUWodfrG3WNGTNmiA0bNoiMjAyRlpYmnnrqKaFSqcQff/whhBDiwQcfFCEhIWLVqlVi586dYtiwYaJbt27CarU2+nOUlpYKQJSWlp6zTnV1tdi3b5+orq5udL+NwVJYKNITeot9kVEivXcfYSksbNL+L5bU1FRx1113idDQUKHT6YSbm5vo0aOHePbZZ0V+fr4QQojExEQBNPhKTEx09GU2m8WsWbNEbGys0Ov1Qq/Xi9jYWDFr1ixhNpvrXHft2rX1+nJzcxMxMTFi5syZ5/3bXOk01/9mU2G2mcUr214RcUviRNySODHp90miqLrIUV5+qlosezVZvPPAavHOA6vF7x/uEcYqSytafH4sZrP4+a054tXxo8Wr40eLpG+WClmWW9usvy2Nuf/W0qKPD15eXg0GJmzatAmwj9bOhUsjgyFOnjzJHXfcQW5uLl5eXsTHx/Pbb78xcqQ9jP6NN95Ao9Ewfvx4qqurGT58OEuWLGn2NXBNhcbHh6AXnifvf7MI/O/TrZamq5Zu3bo5dmo/F+vWrWtUX1qtlhkzZjBjxowL1h0yZMglZ8VRcF4Kqgp4bP1jpOSnADC562Qe7v6wYwnA0ZSaJMlVVrR6NVfd3oXIvs6VJPlMjJUV/Pjq/zi+bzcqtZqR9z9C3JDGudoV/jotKnA9e/Zk1apVpKSk0KNHDwCqqqpYvnw5kiQxfPjwBtsdOnSIdu3aNeoaixYtOm+5wWBg3rx5zJs37+KMdyI8r722VdySCgrNyY68HTy+/nGKjEW4a93536D/MSzUvgTEYrax+ZtD7N2YA4B/mAcjJ8XSxr9lAqsuhbLCfJa9/BxF2cfQubhw/b9nEN6tZ2ubdUXRogI3efJkVq5cyciRI3n44Yfx9fXl008/paCggLZt23LTTTfVa3Py5El2797dYJmCgsLfHyEEn+z7hDeS38AmbHT27swbQ94gzDMMgMLsCv5YdEaS5FGh9B3rfEmSzyQ/8yjLX3mOiuJTuHm35ab/PId/eIfWNuuKo0UF7pZbbuGXX37hk08+4cUXXwROh+i//fbbuLm51WuzaNEiZFmuszhbQUHh8qDSUsmzm5/lj6w/ABjdYTTP9nsWV62rfanH2my2LDtiT5LsVZMkOdq5ow4z01L46fVZmKur8QkJ5aYZz+Hpq0T0tgYtHsKzZMkSrr/+epYvX05BQQEhISHce++9DBw4sMH6R48e5YYbbuC6665rYUsVFBSak6MlR5m2bhoZpRloJA3/1+f/uC3yNiRJorrczOpP9pO1uwiA8K4+DLszGhcPXStbfX72rFvFyoXzkG022sd0ZezjT2Nwc74UYVcKrRKjOm7cOMaNG3fhisCHH37YzNYoKCi0NH9k/sEzm5+hylqFv4s/rw15je7+3QE4vq8mSXJZbZLkTnQd0s5pA0nA7ona+t2XJH3zOQBRAxO5eso0NMquFq2KsghDQUGhxbDKVt5MfpOP99k3Nu4d2Js5V83B18XXniT5h5okyYB3kBujJsU6XZLkA1s2smbx+wy750Ei+w/CZrWy6sN32bPW7mbtc8PNDLrtTiSV884RXikoAqegoNAiFFYX8sT6J9hxcgcA98Tew796/guNSkPJySr+WLSXgmP2JMlxV7Vj4M2d0DhZkuSq0hJWLnwHU1UlKz+YR0CHjqz+aAGZqclIkoph9z5I91HKdIqz0KIC16HDpUcRSZLEkSNHmtAaBQWFliIlP4XH1j1GQXUBblo3Xhz4IiPDRiKEYH9SLhu+OojVZEPvpmHYHdF06O7X2ibXQwjByg/mYzba93Q0V1fz6X+mYa6qRKPTM/rR/6NTQt9WtlLhTFpU4DIzM5Ek6ZIW6Dqz/11BQaFhhBAsTV/Kq9tfxSqsdPTqyBtD3yDCK8KeJPnzdA7tyAegXZc2jLjHeZMkH9iy0bEpKdjTb5mrKtG6uHDLf18iqFNkK1qn0BCt4qLs1asXEydO5IYbbmh0hhIFBYW/F1WWKp7b8hwrMlYAcE34NTw/4Hlcta7kHS3lj0V7KS8yIqkk+lwfQc+rw1CpnPNBtqq0hFUfzAck7JnhTiMh4eXXuJ0uFFqWFp0F/fLLLxkzZgxpaWlMnz6dbt26MWPGDPbs2YO/vz8BAQHnfSnY+Tkth94vreSXtNzWNkVBoUEySzP556//ZEXGCjSShid7P8mcq+ZgULuw/ZcMlr26k/IiI56+Bm56oicJ14Y7rbjVdU3W9z5ZTEZWffhuyxumcEFaVODGjx/PDz/8QG5uLvPmzSM2NpYlS5YwatQoQkJCeOKJJ/7SrgFXAoUVJp5atpuCCjMzlqVRWGG6cKNmJC0tjXvuuYeIiAgMBgPu7u707NmTOXPmcOrUKcCeNzIu7vw7Fd999924u587Ws7d3Z27777bcbxu3Tr7/npnvLy9venbty8ff/xxvfaVlZXMnj2bbt264enpiYeHBx07dmT8+PHn3SvQGah17V8o56ezsDprNbf9chuHSw7j6+LLoqsXMTFmIhXFJn54I4U/f7LvANClTwC3Pt2HwAjn3AGgloKsDA5v34KQ5QbLhSxz6M8kCo9ntbBlCheiVVyUbdu2ZerUqUydOpXMzEw+/fRTli5dymuvvcbrr79OdHQ0d9xxBxMmTLikjUUvV4QQPL18N5Vm+87XlWYb//1+Dwsm9moVez744AOmTp1KZGQkTzzxBDExMVgsFnbs2MGCBQvYsmULy5cvb1YbZs2axdChQwEoLCzkk08+4e6776asrIxHHnkEAJvNxqhRo9i9ezdPPPEEffr0Aew5Tn/66Sc2btxIYmJis9p5JWCVrcxLmcdHez4CoKd/T15NfBU/Vz+OpOSz9tN0R5LkxNu7ENkvqJUtvjAn0vexatH5R2eSSkWnhH74tg9rIasUGkurLxMIDw/nmWee4ZlnnmH79u18/vnnfPXVVzz11FO88cYb5OXltbaJTsPPabn8vvek49gmC37bk8fPaTmMiQ9uUVu2bNnClClTGDlyJN9//z16vd5RNnLkSB577DF+++23Zrejc+fO9OvXz3F83XXXsX37dr744guHwG3YsIGkpCQ++ugj7rnnHkfdq6++mocffhj5HE/mCo2nqLqIJzc8yba8bQDcEXMH/+71b7CqWPt5Ovv+RkmSASpLitnw+WL2bVgDgM7VFdlqxWq2UNdNKaFzcWHE5KkN9qPQujjVSsSwsDA6dOhAcHAwQojL7sYjhKDKbL2k1/FTlTy1fDdnz1JIwFPLdnP8VOUl9XupW87MmjULSZJYuHBhHXGrRafTMXbs2Evq+6+gUqlwd3dHe0YGiaIie7qnoKCGRwyqMxbkFhQUMHXqVGJiYnB3d8ff359hw4axcePGOm1q3YZz585l9uzZhIeH4+LiwpAhQzh48CAWi4X//Oc/BAcH4+XlxT/+8Q/y8/Pr9BEeHs6YMWNYvnw58fHxGAwGOnTowNtvv92oz3ro0CEmTJiAv78/er2e6Oho5s+f36i2Tcmugl2M/3k82/K24aJxYW7iXP6v9/9RmmPkm1nb7eImQc+rw7jpiV5OLW6yzUbyLz/w0bQHHOLWddgoJr31AVdPmUb9OTjByPsextWrTYvbqnBhWn0EV1VVxbJly/j8889ZvXo1NpsNLy8vJk+ezJ133tna5jUp1RYbMc/+3qR9CqDMaGXwnHWX1H7fC1fjqru4fwObzcaaNWvo1atXq7uQZVnGarUCdiFbvHgxe/bsYeHChY46CQkJaLVaHn30UZ599lmGDRt2TrGrnTecOXMmgYGBVFRUsHz5coYMGcLq1avr7So+f/584uPjmT9/PiUlJTz22GNcf/319O3bF61Wy0cffURWVhaPP/44kydP5scff6zTPjU1lWnTpvHcc88RGBjI559/zqOPPorZbObxxx8/5+fet28fAwYMIDQ0lNdee43AwEB+//13/vWvf1FYWMjMmTMv5dd5UQgh+PrA17yy/RWsspVwz3DeHPomHbw6sGv1cZKWH0a2CnuS5HtiaB/l3EmSs/ftYfVH7znm0gI6dGL4vVMI6mwP/4/sP5gDSRs5krwNIcsO12Rkf2XneGelVQROlmV+//13PvvsM3788UeqqqrQarWMGTOGiRMnMmbMGHQ6506qeiVTWFhIVVUVERERrW0Kt956a51jlUrF008/zX333ec4Fx4ezoIFC3j00UeZOHEiYB/NjRw5ksmTJzN48OkbVGRkJO++e3rOxWazcfXVV5OZmcnbb79dT+DatGnD999/7xgFFhYWMm3aNKKiovjhhx8c9dLT03nzzTcpKyvD09PTcT4nJ4eUlBS6desGwLXXXkt+fj4vvvgiU6dOxdW14dHO9OnT8fDwYNOmTY7+Ro4ciclk4pVXXuFf//oX3t7ejf49XizV1mpe3PIiPx39yX7tsJG8MOAFVEYdv8xPI2tPTZLkeF+G3RmFi7vzfp8rThWx/rOPSN9sDzYyuHsw6LY76Tp8FCrV6UwqkiQx8r6HOL43DVNVpeKa/BvQogK3bds2xxxbQUEBkiQxcOBAJk6cyPjx42nT5vIe5rto1ex74eqLbieE4NEvU1mbXoCtAZeiWpIYFu3HW7f1uCSb/s7Mnj2bYcPsm2KWlJSwevVqXnnlFUwmE3PnznXUu/feexk3bhy//vorW7duZevWrXz22Wd8+umnzJ49myeeeMJRd8GCBSxcuJB9+/ZhMp2OUo2Kiqp3/euuu66OizM6OhqA0aNH16lXe/7YsWN1IkpjY2Md4lbLhAkTWLlyJTt37mTQoEH1rmk0Glm9ejVTpkzB1dXVMYKtteedd95h69atXNtMm+IeLzvOtHXTOFh8ELWk5t+9/s2dMXdyfP8pVi1JpbomSfLAmzsRl+i8SZJtVispK34k6dsvsBirQZKIH341g267ExcPzwbbuHq1YeT9DztyUSquSeemRQWuf//+SJJETEwM//73v/nnP//Z6i6ulkSSpIt2B9byyrh4hr26jnKj9awpbnDTq3n5pvhL7vti8fX1xdXVlYyMjCbrU6PRYLPZzllutVrrzKvV0qFDBxISEhzHI0aMoLi4mNdee41JkybVESUvLy9uv/12br/9dgD27t3LiBEjHCO+Nm3a8Prrr/PYY4/x4IMP8uKLL+Lr64tareaZZ55h//799a7ftm1dt1ut5+Fc541GY53zgYGB9fqsPVc7d3g2RUVFWK3W8+5MX1hY2OD5v8q64+t4auNTlFvKaWtoy6uJr9LTtxdJy46QWpMkuW2wPUmyTzvnSpJ8Jsf2pLFm8QKKsu02B3bqwvB7pxDYsfMF20b2H6y4Jf8mtIqL8siRI7zwwgu88MILjW4jSRKVlZXNaJVz4+uu53//6MojX6TUOS+AWTd1xde9fqBHc6FWqxk+fDgrVqwgOzubkJCQv9xnQEAARqORU6dO1ROHoqIiTCZToxf7x8fH2zfLTEtrcNRVS2xsLLfddhtvvvkmBw8epE+fPnz22WcMGTKE9957r07d8vLyi/9QjaChKOHacz4+Pg228fb2Rq1Wc8cdd/DQQw81WKep3cc22cb81Pl8sPsDALr7defVxFfRV3jw3Zzk00mSE9sxcJzzJUmupfxUIes/WcSBLfagIRcPTwZPuJu4ISOU7P+XIS0ucEKIek+xCo1jTHwQP6flsGp/PjZZoFZJjIwJaPElAgAzZszg119/5b777uOHH36oN2dqsVj47bffuP766xvV34gRI5g1axZfffUVU6ZMqVP29ddfO+o0htpkAf7+9l2Ui4qK8PDwaHBeNz09HYDgYPvvUJKkelGhaWlpbNmypVm8DXv37mXXrl113JRLly7Fw8ODnj17NtjG1dWVoUOHkpKSQnxeV7HzAAAgAElEQVR8fLPPVxcbi3lyw5NsybXnYZwQNYHHej3GkT8L2fDVdqdPkgxgs1pI/uUHtn73JRaTEUlSET/yWgbeOhEXd4/WNk+hmWhRgbvcwv5bGkmS+N8/urLlyDrKjFbcdGpeuvH8GUKai/79+/Pee+8xdepUevXqxZQpU4iNjcVisZCSksLChQuJi4tzCFxZWRnffvttvX78/PxITExk6NChjB07lkcffZTMzEwSExMRQrBhwwbeeOMNxo4dWy/AA+yh8lu3bgWgtLSUVatWsWjRIhISEhzBI2vXruXRRx/ln//8JwMGDMDHx4f8/Hy++OILfvvtN+68807HKHTMmDG8+OKLzJw5k8TERA4cOMALL7xAREREnbmupiI4OJixY8fy3HPPERQUxGeffcbKlSuZPXv2OQNMAN566y0GDRrE4MGDmTJlCuHh4ZSXl3P48GF++ukn1qxZ0yT27Sncw/R108mtzMVF48LM/jMZETiKtUsOcLg2SXJkG0bcHYu7d8t5ES6GrLRU1ixewKmcbACCukQx/N4pBER0bGXLFJodoXDRlJaWCkCUlpaes051dbXYt2+fqK6ubvLr/7TrhEh48Q/x866cJu/7YklNTRV33XWXCA0NFTqdTri5uYkePXqIZ599VuTn5wshhEhMTBTYvan1XomJiY6+zGazmDVrloiNjRV6vV7o9XoRGxsrZs2aJcxmc53rrl27tl5fbm5uIiYmRsycObPO3+b48ePiv//9rxg4cKAIDAwUGo1GeHh4iL59+4p58+YJq9XqqGsymcTjjz8u2rVrJwwGg+jZs6f4/vvvxV133SXCwsIc9TIyMgQg5s6d26Bd33zzTZ3zixcvFoDYvn2741xYWJgYPXq0+Pbbb0VsbKzQ6XQiPDxcvP7663Xa1l5r8eLF9c7fe++9ol27dkKr1Qo/Pz8xYMAA8dJLL533b9aY/01ZlsU3B74RPT7pIeKWxInRy0aLg6cOipzDJeLjGZvFOw+sFu9OWSN2rMgQNpt83uu1FqUF+eLH12aJV8ePFq+OHy3mT54gdq9dKWSbrbVNU/gLNOb+W4skxCWu9L2CKSsrw8vLi9LS0joh32diNBrJyMhw5GhUUDib8PBw4uLi+Pnnn1v0uhf63zRajczaNovlh+1p1oa2H8qLA17i4Ooitv+SiZAFnr4GRk2KIyCi4f//1sRqsZD883K2Lv8Kq8mEJKnofvVoBoz/JwY35w18UWgcjbn/1tLqC70VFBSch+zybKavm87+U/tRSSoe6fEItwRPYPU7+8k9XApAl74BJN4Wic7F+W4fmanJrFnyPsW59tRg7aJiGHbPg/iHX/pmywp/X5zvP1RBQaFZKTeXk1eZx4kTJxjR8XTgzsbsjfxn438oM5fhrfdmTuIc/HI78M3/dtiTJBvUJN4eSWTf+ksbWpuygnzWfvyBY0NSV682JE68l+jBQ512HZ5C86MInIJCK5GZmdni17TKVvIr85GFzPyU+fQI7oG3wZsFuxawYNcCBIKuvl2Z0/9VDv1axm+b9gDgH+7JqEkxePk5Vx5Jq9nMjp+Wse37b7CaTUgqFT2uuZ4Bt0xA7+rW2uYptDKKwCkoXCEIIcipyEHGHs1cba1mZtJMbMLGphObALg18lYmBU5l7dsHKc6rciRJ7nN9BGq1c60TO5qynbWLF1Jy0r7xb0h0HMPufRC/0PDWNUzBabjsBO7ll19m2bJlpKen4+LiwoABA5g9ezaRkZGOOiaTiccff5wvvviC6upqhg8fzrvvvtskC5bPRonhUXAWysxllJvL0VkNuJnaEFTcmfWmdQDo1Xqe6fcs4Vk9+P7VXchWgVtNkuQQJ0uSXJqfx9qPP+DIDvvWPG7ebUmceC9RAxMVd6RCHZzrkawJWL9+PQ899BBbt25l5cqVWK1WRo0aVScLyrRp01i+fDlffvklmzZtoqKigjFjxpw3VdTFUptWqqqqqsn6VFC4VKyylZyKHFRChbbKgLBC9/RrMFjckZB4q987qFaEsembQ8hWQXi8L7c+08epxM1iNpH0zVKWTJ/KkR3bUKnV9BrzD+55fQHRg4Yo4qZQj8t+mUBBQQH+/v6sX7+eq666itLSUvz8/Pj0008dmehzcnJo3749v/76K1dfXT8ZsslkqpN0t6ysjPbt218wTDU3N5eSkhL8/f1xdXVVvoAKrYIQgtzKXCrNlegr3SgtLCM7rZLslCoy2+4mPWArozLuRmM0oNaqGHRzJ2Kvcq4kyUeSt7F2yUJK8+0b/raPjWf4vQ/iExLaypYptDTKMoEzKC21hzbX5jdMTk7GYrEwatQoR53g4GDi4uJISkpqUOBefvllnn/++Yu+dm3S3LM3ulRQaEkssoWCqgI0Nh06o5m8A0ZyUkyoUNPhVHc6nOoOgHuAljH393CqJMklebms/XghR3duB8C9rQ+Jd0wisv9gpxJgBefkshY4IQTTp09n0KBBji1K8vLy0Ol09fbKCggIaDDxLdjzLk6fPt1xXDuCuxCSJBEUFIS/vz8Wi+UvfBIFhUuj0lzJ8kPL+TH9F67ddz+iwoSw1J+ZKA3P4oHpdzpNkmSLycifP3zL9h+/w2axoFJr6DX6BvqNuw2dwaW1zVP4m3BZC9zDDz9MWloamzZtumBdIcQ5nwj1en29BLwXg1qtRq12jhuHwpWByWbiy/Qv+XD3h5QYSxh14F6sxRpUDUy7C2RiPeOdQtyEEBzesZV1H39AWYHd8xHatTvD7nkAn3ZXztZaCk3DZStwjzzyCD/++CMbNmyoEx0ZGBiI2WymuLi4ziguPz+fAQMGtIapCgpNhlW28tORn3h317vkVdo9EnGqXnQo7nbONhIqstNKKcqpwCe49dyTxbknWLNkIZmpyQB4+Pgx5K7JdO4zQHFHKlwSl53ACSF45JFHWL58OevWrau3L1avXr3QarWsXLmS8ePHA/ZgkD179jBnzpzWMFlB4S8jhGDN8TW8vfNtjpYeBSDANYApsVMJP9GDPzWZyNaG48kkFUR082s1cbMYjWz7/mt2/LQMm9WKWqMh4fqb6HvjeLRKHleFv8BlJ3APPfQQS5cu5YcffsDDw8Mxr+bl5YWLiwteXl5MmjSJxx57DB8fH9q2bcvjjz9O165dG73fmIKCM7E9bztvJr9JWmEaAF56LyZH3U9c/iB2L8lha1nNzusS0IDG6QwaEm+PrF/QzAghOPRnEus+/pDyogIAwrv3Yuhd99M2uF2L26Nw+XHZLRM4lytj8eLF3H333YA9m/oTTzzB0qVL6yz0buyGlhcTpqqg0FzsL9rPWylvsfnEZgBcNC5M7HwHfU9dw941J6kuMwPg4WMg4dpw1FoVqxbvq9fPqMmxdE5o3G7pTcWpnGzWLH6frDT7DvWefv4Mues+OiX0U9yRCuflYu6/l53AtQSKwCm0JsfKjvFOyjusyFwBgEbSMC7iFoaU/4ODawupLrdH7Hr4GEi4LpzIfoGo1SqEEPz2/m4y0goR8mnX5LUPdG0x283GarYu+4rkn79HtllRa7X0HjuOPjfcjFavuCMVLoyyDk5B4TKkoKqA99Pe57uD32EV9t3Fr2s/hmsqbifjx1J2ldtzMnr6Guh17Wlhq0WSJBInRJF9YCvmamuLuiaFEBzcuol1n3xIxakiADr07M3Qu+6nTWBQi9igcOWhCJyCgpNTZi5jyZ4lfLb/M6qt1QBcFZDIWONdnFhhZF95IQCefi4kXBtOl74B50yM7OqpY8g/I9n09SEG39oFV09ds9tflH2cNYvf49iemjlC/wCG3n0/HXv1bfZrK1zZKAKnoOCkGK1Gvkz/kg92f0CZuQyA7t49udl8H4WrBIcrygG7sPW+LpwufQJQNSLjf+eEgBaZczNXV7Hluy/Z+esPyDYbGq2O3jfcTO8bxqHVXfq6UgWFxqIInIKCk2GVrfxw+Afe3fUu+VX2xc5d3KK41TqF8vVasivtwSNe/i4kXBdOl96NE7aWQghBetIG1n+6iMriUwB0TOjL0Lvuw8vf+TZLVbh8UQROQcFJEEKw6tgq3t75NpllmQCE6EOZYH0I0xZ3CiqtgIU2Aa4kXBtGZycTNoDCY5msXryA7H32jVLbBAQx9J776dCjdytbpnAlogicgoITsC13G28mv8meIrsw+KoDmGB9COlPH8qqrIDVLmzXhduFTeVcofSmqkqSvllKym8/IWQZjU5P3xtvIeH6m9Domn+eT0GhIRSBU1BoRfYW7eWt5LfYkrsFAE/aMMH6MLpdgZirbIAV70C7sHVKcD5hE0Kwf9M61n+6iKrSEgA69e7P0Lvuw9PPv5WtU7jSUQROQaEVyCzN5J3Ud/g983cAXGR3bjVPwSM9DEu1DTM2u7CNDqdTL+cTNoD8zKOsWbyAE+n2xePeQcEMu/sBwrv3amXLFBTsKAKnoNCC5Ffls2DXApYdWoZN2NBbXbnJOBnfw52xVMtYsOEd5Ebv0eF07OnvlMJmrKwg6evPSf39F4SQ0ej19LvpNnqNvhFNzU72CgrOgCJwCgotQKmplI/2fMTS/Usx2ozorC7cWHknQUdjsRoFFmTaBrvZXZE9/ZGcUNiELLN3wxo2Ll3icEd26TeIxDsm4enr18rWKSjURxE4BYVmpNpazdL9S1m0ZxHl5nJ0VhfGlN5DWFZ3bCawImgb7Ebv0RF07OHnlMIGcDLjCKs/eo/cg+kAeAeHMPyeBwmL797KlikonBtF4BQUmgGLbOH7w9+zIHUB+dX56C2ujCr+J52y+yCbwAb4tLMLW4fuzitsxooKNn31KWkrVyCEjFZvoP/Nt9PzurGoNYo7UsG5UQROQaEJkYXMyqyVvJPyDpllmegtrgwrvJXInP4Is4QM+LRzp/eYcDp0c15hE7LMnnWr2Lh0CdXl9iwqkQOuIvGOe/Fo69vK1ikoNA5F4BQUmoiknCTe2vkW+4r2YbC4kZh/M9G5A8GiQgA+Ie70GR1BRDffVhW2A1s2smbx+wy750Ei+w+qV37y6GFWL3qP3MMHAPAJCWXYPQ8SGhff0qYqKPwlFIFTUPiL7Cncw5s732Rb7jYMFjcG5v2DrievAos9y4hve3d6j44gIr51hQ2gqrSElQvfwVRVycoP5tE+Jg5XrzYAVJeXsenLT0hb/TsIgc7Fhf43T6DHNdej1ii3CoW/H8p/rYLCJZJRmsG8lHmszFqJweJG/9wbiD+ZiGRVA3Zh6zMmgvB4X6fYxFMIwcoP5mM22nckMFdXs+rDdxnz7yfZs2YlG7/4GGNNAufoQUO4auK9uHu3bU2TFRT+EorAKShcJHmVeSzYtYDvD3+P1mygX85Y4vMTUVntXye/UA96j4kgvKuPUwhbLQe2bOTw9i2OYyHLHPoziY8efYDS/DwAfEPDGX7Pg4TExLWWmQoKTYYicAoKjaTUVMqi3YtYmr4Uyail94nRdmGz2b9G/mEe9B4dQZiTCRvYXZOrPpgPSICoU1aan4fW4MKgWyfS/eoxqNTqVrFRQaGpUQROQeECVFmqWJq+lI92f4S1EnrkXEPX/MGobfYwef8w+4gtLM75hA3Odk2KBuuERMfS87obWtYwBYVmRhE4BYVzYJEtLDu4jAVpC6gsNdE9ZzhxJwehlmuELdyTPmMiCI1t65TCVkt2+t46rsmGyEjZQeHxLHzbh7WQVQoKzY8icAoKZyELmd8zf2deyjyKikrpnjOc2JMDHcIWEOFJ7zERhMY4r7CZjdUc3r6V9E3ryNi187x1JZWKTgn9FHFTuOxQBE5BoQYhhGMtW1beCbqfGM7V+QPR1AhbYAdPeo+OoL2TCpvNaiFzVwr7N63jyI5tWM0mR5lfWASnck5gs5jPaiWhc3FhxOSpLWusgkILoAicggKQVpDGmzvfZO+xA/Q4MYKB+ZPOEDYv+oyJICTa2+mETcgyJw7uJ33TOg5s3YyxJusIQJvAIKIHDSFqYCJtg0NIT9rAL2/NObsHRt73sGMtnILC5YQicApXNEdLjvJ2yttsPbSD7jnDmXDyVjTCLmxBHb3oPSaCkCjnE7aCY5mkb1pHetIGygryHeddvdoQNeAqogcNIaBj5zp2R/YfzIGkjRxJ3oaQZYdrMrL/4Nb4CAoKzY4icApXJHmVecxPnc/qfevplj2M2/OfOS1snWqELdK5hK2sIJ/9m9eTvnk9hccyHed1Li507jOAqEFDCI2NP2eYvyRJjLzvIY7vTcNUVam4JhUuexSBU7iiKDGW8MHuD/hp1wpijydyW/5/UQv71yC4cxt6j4mgXZc2TiNs1eVlHNy6if2b1jl2zgZQazRE9EggauAQOvTqjVanb1R/rl5tGHn/w45clIprUuFyRhJCNLww5m/Mhg0bmDt3LsnJyeTm5rJ8+XJuvPFGR7kQgueff56FCxdSXFxM3759mT9/PrGxsY3qv6ysDC8vL0pLS/H09Gyuj6HQhFRZqvh036d8nbycyKwBROX3qyNsfcZE0C7Su5WttGMxGjmSvI39m9aRuWsnss1mL5Ak2kfHETVoCF36DsTg7t66hiootAIXc/+9LEdwlZWVdOvWjXvuuYdx48bVK58zZw6vv/46S5YsoUuXLrz00kuMHDmSAwcO4OHh0QoWKzQXFpuFbw99yyfbviDiaAI35E93CFu7LrUjttYXNpvVyrHdqezfvJ7Df27BYjI6yvzDOxI1KJGoAVfh4aNsVaOg0FguyxHcmUiSVGcEJ4QgODiYadOm8eSTTwJgMpkICAhg9uzZPPDAAxfsUxnBOT+ykFmRsYJFSZ8QdDCOyIK+p4Ut0j5iC+7cusImhCD3UDr7N63jwJZNVJeVOsq8AgKJHphI1MAh+IS0b0UrFRSciyt+BHc+MjIyyMvLY9SoUY5zer2exMREkpKSGhQ4k8mEyXR6TVFZWVm9Ogotx7cr/iBzhZGI6wyMu2ZUnTIhBBtPbGThpsV47evIkIJJZwlbB4I7t+68U1H2MfZvWk/65nWU5p90nHfx9CKy/2CiBw0hqHOk08wDKij8XbniBC4vz541PSAgoM75gIAAsrKyGmzz8ssv8/zzzze7bQoX5kR+Hsd/tmCwuZH1s5ETPfNo5x8IQGp+Ku9u/BDdrkD6FExALezRhMGRXvS9viPBnVpP2MqLCknfvJ79m9dTkHnUcV5rcKFz735EDRpCWNfuSqJjBYUm5IoTuFrOfjoWQpzziXnGjBlMnz7dcVxWVkb79orbqKWRZZlP31+Fmy0ACQmtVcdnC1cx7qF+vLvxA8w7PIkruMEhbEGRHvQf24Wgjl6tYq+xooKD2zaRvmk9x/fvgZrZAJVaTXj3XkQPTKRjQl+0ekOr2KegcLlzxQlcYKD9aT8vL4+goCDH+fz8/Hqjulr0ej16fePCsBWaj2W/rcLjRLDjWIUa9+xgPpq1iojyEaiwC1tApBuDbogisEPLC5vFbOJo8nb2b1pHRsoOZJvVUdYuKpboQUPo0m8gLh7K3K2CQnNzxQlcREQEgYGBrFy5kh49egBgNptZv349s2fPbmXrFM7Fifw8jv9iQYsKCZXjvEDQrrwLAL5dXEi8MabFhU222Ti2Zxfpm9dz6M8kzNXVjjK/0HCiBg0hauBVePr6t6hdCgpXOpelwFVUVHD48GHHcUZGBqmpqbRt25bQ0FCmTZvGrFmz6Ny5M507d2bWrFm4uroyYcKEVrRa4VxYbVYWv7cCL1v7OuIGICEhEFT7FXHr9PEtZpMQgrwjB+0RkEkbqSotcZR5+vkTNTCR6IGJ+IaGt5hNCgoKdbksBW7Hjh0MHTrUcVw7f3bXXXexZMkS/u///o/q6mqmTp3qWOj9xx9/KGvgnIhqazVbsreQlJrCqRQbHU8mnLOuhIRrgS+p6fvoHhXTrHadysl2RECW5OU6zhs8PInsN4ioQYm06xKNpFKdpxcFBYWW4LJfB9ccKOvgmoeCqgLWHt5Ays6DGI9oaFcciYv1dLYOgUCifiCQjI2qkJM8+d+JzWJXxaki0pM2kL55PSePnvYMaPR6OiX0I3rQEMLie6DWXJbPiwoKToWyDk7hb4EQgoPFB1m7ezOHduWhz/YhqKwDIfQ9XUdnpW0XPeGR/mz7PgOtTXfWHJyMRWNm4v0jmtQ2U1UlB7dtJn3TOo7t3e2IgJRUKsK79bRHQPbuh87g0qTXVVBQaDoUgVNoUSw2C9tO/Mnm5BTy9lfgmx9OG2M4HQg/XcnbRHhXH7ondCaooxcqtV3QcizHOfnj2XNwKkJHax1r4f4KVrOZjJQd7N+0jqMp27FZLI6y4C7R9gjI/oNw9WydZQcKCgoXhyJwCs1OibGEdYc2krLjEFVH1AQVd8LdFkOnmnKhktG3txHTI5S4XuF4+TU8KrrpmhHMSV6K24kAVKgdrsmHrr1016Qs2zi+d7c9AnJbEqaqSkeZT0hozYahV+HVBAKqoKAAP6fl8PyPe3lubByj44Mu3OAvoAicQrOQUZLB2tQkDqXlojnuTUB5GP50d5TLBjNtu+jo2acLHeMC0Rku/K+oUqm444ERfP18Mlqb4ZJdk0II8jOO1ERAbqCi+JSjzN3H17FhqF9YhJIuS0GhCSmsMPHUst2UGa3MWJZG3w5t8XVvvjXGisApNAlW2UpKTiqbt6eQs6+ctidD8TAHEcLpJzThU01YVx8S+kYRGOaFpLp48WjnH0j7MVoyV1QScZ3holyTxXk5pG+yp8sqzsl2nDe4udOlJgIyJCpWiYBUUGgGhBA8vXw3lWb79k+VZhv//X4PCyb2arZrKgKncMlUWipZn76ZlB2HqDgsEVAcgVbuSFhNuayyoguzEtszjO4JHXH3bpqUVF3buFBg/IQ4rwcvbGNJMQe2bGT/pnXkHT7oOK/R6uiQ0JfoQUMI79YTjVbbJLYpKCg0zM9pufy+93RycZss+G1PHj+n5TAmPvg8LS8dReAULoqcshxWp2zmUGoe6mNe+FaG0IZoatMYW12NtI3UktAnik6xwWh1TZs8uKq0hJUL38FUVcnKD+bRPiau3q7UpqoqDm/fwv5N6zi2exdCyABIkoqw+O5EDUykc5/+6Fxcm9Q2BQWFhimsMPH08t1IwJnr0iTgqWW76dfBp1lclYrAKZwXWcjsztnLxm07ydtfgWdeMG4WHwLwAexr04RfFaFxbenXPxb/9l7NNm8lhGDlB/MxG+2psMzV1az68F3GPvYUVouFzNRkewRk8p9YLWZHu6BOkUQNGkJk/0G4tWn9zU0VFK4krDaZf32RQrnJytmLrgVQaWo+V6UicAr1MFqNbErfRvKfB6g8LOFbHIZatHfMptnUFnThZmJ6htIrIQo3r5ZJRH1gy0YOb9/iOBayzKE/k/j6xacpyDiCsbLCUeYdHEL0oESiBibiHdg87g8FBYX6FFWYSD1ews5jxezMKiH1WDHVVvmc9W3C7qo8eLKcLgFNm01KETgFAPIrC1izfQsHU3NQHfPEuyoQdzpRm0fE4laJd5SWPn2i6RLTHrW2ZQMxqkpLWPXB/AbLju/ZBYCbd1tHBKR/REclAlJBoZmx2mTS88pJOVbMzmMlpBwrJrOoql49tWSfc6OB76RKtjGyk2eTixsoAnfFIoQgPfcQG7Ymk7OvHI+8QFys7vhgz8wvSzI2/3LC4toyYEA3/IObz/XYEFaLhVMnjlOQlUF+5hH2b1pfZ41aXSTaRcUwfuYsVCplw1AFheaioNxUR8zSskupttjq1evk706P9m3oGeZN90ADxn/dwsQOj1Cl1SOk0w/HkpBxtZp4+I8X4f4hTW6vInBXEBbZQtK+HST/eYDyQ9C2uB0qAvDHvg+eRWtEG24mtmcofXvH4eKuaxG7KkuKKcjKoOBYpv09K4NTJ44j2+p/cRpGcCJ9L6dOZOPbPuzC1RUUFC6IxSazP7eMnVnFpNS4HI+fqq5Xz8OgoXv7NvQM9aZHsIF46xEMaWsw/pmC8YssqrIroUzLI+Xf8krvO+q0FZKKR1K/Q5OTg2nb7+j7Xt2kn0ERuMucU5XFrP1zGwdTc+CYO57VvugJpXbWzOhRhncXDX36xhATG+5Ii9Uc2KxWinOy7aOyGiEryMqos9XMmRjc3PELi8AnNIycA/spyMpAyPV9+ZJKRaeEfoq4KSj8BfLLjOw8VkzKMbuYpWWXYjpr7kySoLO/u13MggzEmw7R7uAazLvSMC7PxnTSyMlyNdRJim5fgnPViVQ2tOvG1sBYZJUalWyjf+5erspNxSPau8nFDRSBuyw5nJPB+i3J5O4rxy3PH53NBc+aXI82yYY1sJT2cd4M7t+DwOC2zWJDdXmZQ8BqBe1U9jFsVmv9ypKEd2AwfmERdV4ePr4Ot2hVaQkfTXsAU1UVZwca61xcGDF5arN8DgWFyxGzVWZvTqlDzFKOlXCipP7ozMtFS4/QNvQI1BNvPEyXzCS06bswrsjFmG/BZlRzok4Lu6RoPLUYwgIwxESj7zkQTVAQxyfdzyOp37FrRGcqtQa7a3LXd6i0EPjWx83yORWBuwyw2qxs25dK8rYDlB8Gr5IAJNrijV28zLoqNOFGonuEMrBvN1xcmy7qUbbZKM7NoSDr6GlBO5ZJxamiBuvrXFzxCwuvI2S+7cPQ6s+/CNzVqw0j7nuIX96ac1aJYOR9D9dbC6egoHCa3NJqu5jVuBt3nyjFfNboTCVBlwAPegTq6WrKICb7T/wPJmNeV4CpwIZsVVFWp4V9vlvnq8cQ0Q5DbByGhKvQ9xqAxrv+cpygh29DfuMr/pX6LQvib2BK2ve0MVcSNP12NKFdmuVzK/vBXQLOsB9cWVUFa7dt40DKCchyx81U9wZf5XWKNl009O0bS9eYTpeUFutsjJUVZ4zK7PNlRcez6qw5O5M2AUGnRSwsHP+wCDz9Ai45WEUIwY+vzeJI8jaELDtck2Mfezn8tQ4AABxJSURBVOqvfCwFhcsKo8XG3pwyUs5wN+aWGuvV83bV0j1AT1drNjF5qXQ8nIz6WC6mYgFy/e+opAZ9oBuGDiEYunbD0GcY+vjeqFwblzBByDInbhpI+YFiEBJIAo8ob0KWb7lw4zNQ9oNzcr5d8QeZK4xEXGdg3DWjGt0uMyeb9UnJ5Owtx+WkL1pZhxshAFhVFsyBxbSP8+aqAT0JDvS7ZPuELFOcl0vhsYw682XlhQUN1tfqDfiGheMXGo5fWAe7qIWGNXmmEEmSGHnfQxzfm4apqlJxTSpc8QghyCk12kdmNWK2L6cMs63u6Eytkoj01tBV5BNTtI8uman4ZhzFWnZayKw1L5BQ6cEQ7ImhcziGrj3R9xuOPro70l/Y1FdSqQh8+2MqR49FtohmdU3WoghcC3MiP4/jP1sw2NzI+tnIiZ5550wYLNtktu/ZzY7tByg/JPAo9QM88MS+XqRaX44mvJronu25qu8AXAwXn+vRVFVF4RnRiwVZGRQcz8RqMjVY39PPv95cWRv/wBZLUOzq1YaR9z/MmsXvM+yeBxXXpMIVhdFiY/eJUnuofpZd0PLL639X2+ol4jXFxBYfpkv2HsKPHkBXYalTx1oTCKJxlzCEeGPo0hFD997o+45E2yGyWZYFaUK7EPTI7eS99wWBUyc0m2uyFsVFeQlcqotSlmXm/K/+fmZP/vf0fmaVldWs27qdA6knEFluGMzujjKBTIV3IW26aOjTJ4bu0VGoGiksQpYpLcivO1eWlUFp/skG62t0enzbh9YTM72rW6M/r4KCwqUjhCC7uLpOZOO+nDKsct1btkaCLpoKYsoziMxJp3PmQfzLiqkvTwKdtxpDqC+GqC4YevRD3+9qNIEhLfWRmgTFRemkLPttFR4ngrGZD2CsWovWdRju2V345KufUavU5OwtRX+yLWqhQV+7Nk1twhhYRPu4NgwZmNCo7WEsRmOdNWUFxzIpPJaBubp+lBTY90Dzr50rC7UHgHgHBSuLphUUWpAqs5Xd2aXsPCOysbCi/ujMVzIRW51NZN5BOmcfoVPxCQy2uqMzSS3Q+2oxhAdgiIrC0Gsw+r4jUXk1T9S0s6IIXAtxIj+P479Y0MhGLFWrQJiwVK1E0rSjfK19VOSKPwCVhmJUEdVE9whhSO9E3M4xlyWEoLywgIJjGRRk1opZBsV5udDAwFyt1eITEopf6JmjsnBcPFonUEZB4e9IU+xILYQgq6iKlON2V2PK8WL255bb01mdgQaZzsaTRBUepUtuBtGnsvCvrjs6U+lkDMEGDBHBGGLj0Cdchb7XMCSD4m1RBK4FkGWZT99fhavVH2vVShA1UYfCjLVqNVr36zFpqjAkVNC7Twy9ohJRnzV6sphNFB0/Vneu7FgGpsqG01e5tfGu5170DmqH+i9MEisoXOkUVpiY8eV2ym1q/vPln/TtcE2jtnmpNFnZlV1CSk2Kq5RjJRRV1o8+9rWUE3Uqg6iCLKJPZdKx5AR6+fTaUY2rjKGjK4aO7THEdUPfeyjaroOQNC2TdejvhnK3awFSD+y3uyYtB5Ath+lpzCE4s4ITEe6kIJDNBzEQycB+HekWGU1FcZFdwDJPi1lxbo5jX7MzUak1+LQLOUPIOuAXFn5ZB1988/rbzDrux9OhBdz873+1tjl/O5piBHIlIoTgqc+3UGmVQCVRaZV4eukW3j8rh6IQgozCSke+xp3HSjiQV8ZZgzO0spVOpSeIKsok+lQWUcVZ+FWX2gslgc5TxhDpiaFzpD2Sse8INJ16gTJ10GgUgWsBukdG80fAFrQHVuEpV3OouhPTh/6DqXuX46ktoqzyd0y6/Rz6xp3NWZkYy8sa7MfF0+u0kNXMlfmEtEetuXJ2oz6Zvo/nTgRTqTPwXLaGwen7CIiKaW2z/jZc6ghEAX7elcMfGZUOgZFVan4/Wsk3O44T5OVSI2b2hdQlVZZ67f2qih1CFnUqi46lJ9DJNvt8WRsZQ6Q3hs7dMXTvg77PSFTtYqCFopMvVxSBawEkSSJCn0W2bCTuVDGTu02hUmvgra638MGul0nydUFVfJRjxTX1VSraBofUczG6tfG+oreAkW02nnh7BdXunUGSqFLr+b95v/LxfEXgGkNjRyBXOkIITFa55mXDZJHJKzPy5FfJIFR1t3wRgie+3QVnxSxqbRY6l2TXETRfYxkqrYzBR2Do7IshKgF9j77oe41E8uvY4FYyCn8NReBagKLjWWTvSqGHKZd57W6hWqOz36A1et4JvoXJZd+w0xDMwPETieiRgE9IKBqd4lM/m2/fnM8GzyjHsaxSs94jmm9ef5tbpiuuygtxrhHIz2k5jIl3rk1hrTbZITJGi62O2DT0s9FSc84q15yv36ZOP1YZk8V2xkvGZJMxWmXMtnOtnFKfrWMOUdJbzfTP3UNU8TGiTmXRoTQHFxcTBl8Vho4BGMZ0x9BrIJr4IUht2iti1kJc0QL37rvvMnfuXHJzc4mNjeXNN99k8ODBTX4dn/ZhxMVEcmizG0nR8Y7zskrN5nbxJJ5MoWuv9vQbd1uTX9tZEUIgC5CFQBYC4fjZvjHi2eX5hw7xXE4waES9J+iZJ4LpsiMN/86dkZCQJPt9SJJO/6xy/Gw/oZJqyrF3p6rps7bO2eV/95FzYYWJp77egSRU9fbjmvHldvqd5aqUZYHZVl8sjLWiUk8szqxnLzeeWc9ixWSxYjRbMZkt9mOz7bTo2AQmG5hsYJbB1sAqrtZAEjIamw3LBaYBTBodk6pXE9NBheH6OPQJ96GJHAQegYqYtSJX7ELvr776ijvuuIN3332XgQMH8v777/Phhx+yb98+QkNDz9v2Yhd6C1km7ebhTAh/qN6GfwiBTrYy9vgm3D01CElCFhJCAhkJgYSt5l1Gspef8XOdsobOSw23lc9RXredquY82FCduz9JhQz2+g2W1z9X53fwN0ESoua3I+zCd8F3+8/2NKCnz6lqfgZQSWfWBenMYwlUnL4/SmfWlzjrXXLUrVMmAUIio1pHiU3T8M1WCAySDTeVDbOQMAo1FpwnkEFjs6KTLejOetfarOhkKzqbBZ1sRVvzrrPVnrPX1dapc0YfjvoWdML+0mNBhwUDZvRY0KpsCBmejZrk2OblbGq3ffno4Z7NsuWLQl0u5v57xQpc37596dmzJ++9957jXHR0NDfeeCMvv/zyedterMBVJ61g0oLUc35BFC6AkKERgqiWrXbpkCQE/C1F1BlRCdkuAjYrWofA2IVBK1vR17yfKRjaWqGpESJ9PRGqaSss6LCix4oeC3rJikGyoZdk9CorOjWoNRKSVo1Kq0bSqJG0GsdLpdMiabVIjnc9Kr39XdLrkXQ6JL0Bld5gPza4IOkNSHoXJBdXVAb7u6RzAZUW1FpQ606/qzQIScOeCddzW9iUBnekdrMY+eLYArp+u7oV/0pXDkomkwtgNptJTk7mP//5T53zo0aNIikpqV59k8mE6YzcjGVlDUc5nous8AEkBdcP8T+bqzUnaCuZ6zzlq2qf4hGoEY6netUZP0sS9rKz6tf+fPr9zDqi5qlf2F817rjTP9vbq2vdeZzuy3FdQK2qtVU6XU8641XbTsXpfmuPkU7bcYYrUa2q6Y8a16AQPLLVyuY20ed8gh5YvI95CQJkG0IWIMv/396dBzV1tX8A/14CJAEDCGqAshT6omxVVKq+qIW6oIVirQqioM5g23FE2X7tiFtF5wWKvtoZy4wUmaotOKLigtWKuIFaK2rFDV+pisqIiHEDF6Qk5/cHJjVNUCHBS8LzmckMOefknsc7Y56cc++5B2AKKOQKMMXfL4WCtfzNWtYnMgUDY8ryl/5mL6ZJXypnCryYTmVgCrS0ZXjxvmWalSmnXBUMDC/CYC/VAYCCvfhcy0uBluMBDHLGAS+1VbZhUJZxgAJ/16Hlb6iOxf3dHoCCcVAoGLa8G4jK7s5ak74JU8BXdhVzqosgFplAJACEAkBoykFoCpgpE4n5i8RhYQbO3LwlaZibgxOJwJmLwAktwYnELUlDKG4pF1mAE1nARGTRkkhEluDEli11ZmYGMfXLAfD6bg3iPv8G6QM0d6SOu7AVXjlrtH+Y8KpLJjiZTAa5XA6pVKpWLpVKUVtbq9E+PT0dS5cubXd/fRysEOxmif1X61v9gh79Lyv88OWX7e7D2P03oAIj1lbgKaf5C9pC/hz//b9xsKHlAloxhQKDw0cjspv2EYjFX41IfbQL7++jEUhrTF16I3KCJ0p+O6exI3XkJO8Of2gwaZ8uPYfzz1+PjDGtvyjnz5+PR48eqV7V1dVt7ict6t+wNGXg/rFYm2MKWJoypE79d9v/AV2I1NMbSxxrNEYgjDPBkndu01q4V+BMTFpGIBe2aj1/cRe2wmsVjUBex+rLbzD/yX6Im5sAxmDR/BzJz/bD6sslfIdGWtElE1yPHj0gEAg0Rmt1dXUaozoAEAqFsLKyUnu1uc9uQqRF+Gv9gkmP/IAW276BSQmx+LD+fzBRyAG0jHwDGy7REoE3oByBBNScUzt/Q2+doxHIG1L+UIi/sAXdnzcg/sIW+mHQyXXJBGdubo6BAweiuLhYrby4uBgBAQEd1u8n/RwR7Gap9gUzxt2y061B6qxMBAKsiPsYYvmLX9Dy51g+N4TvsAwGjUB0Z+rSG5GTvJFfshSTw33ph0En1yUTHAAkJSUhJycHP/74Iy5duoTExETcvHkTs2bN6rA+X56qBGM0NdkOUk9vpLxTg+5NDUhxoqnJtqARiH5YfbkEvc/8D1ZffMN3KOQ1uuRNJgAwefJk3Lt3D8uWLcPt27fh6+uLPXv2wNXVtUP77dFNiPTID1QPu6WpybYLT4pDON9BGCjlCCRozdK3sqMyIXzqsuvgdNHeHb0JIYTopi3fv112ipIQQohxowRHCCHEKHXZa3C6UM7qtvWJJoQQQnSj/N59k6trlODaoaGhAQDg7OzMcySEENI1NTQ0wNra+pVt6CaTdlAoFKipqYFEImnXs/Tq6+vh7OyM6upqukmlHej86YbOn27o/OlG1/PHGENDQwMcHR1h8podz2kE1w4mJiZwcnLS+TjtfSoKaUHnTzd0/nRD5083upy/143clOgmE0IIIUaJEhwhhBCjJEhJSUnhO4iuSCAQICgoCKamNEvcHnT+dEPnTzd0/nTzts4f3WRCCCHEKNEUJSGEEKNECY4QQohRogRHCCHEKFGCI4QQYpQowb1F6enp+OCDDyCRSNCrVy+MHz8ely9f5jssg5Weng6O45CQkMB3KAbj1q1biI6Ohp2dHSwsLODn54fTp0/zHZZBaG5uxqJFi+Dm5gaxWAx3d3csW7YMCoWC79A6pdLSUoSFhcHR0REcx2HHjh1q9YwxpKSkwNHREWKxGEFBQbh48aJeY6AE9xaVlJQgNjYWv//+O4qLi9Hc3Izg4GA8efKE79AMzsmTJ5GdnY2+ffvyHYrBePDgAYYOHQozMzP8+uuvqKiowMqVK2FjY8N3aAYhIyMDWVlZyMzMxKVLl7B8+XKsWLEC33//Pd+hdUpPnjxBv379kJmZqbV++fLlWLVqFTIzM3Hy5EnY29tj9OjRqmf96gUjvKmrq2MAWElJCd+hGJSGhgbm4eHBiouLWWBgIIuPj+c7JIMwb948NmzYML7DMFihoaEsJiZGrWzChAksOjqap4gMBwC2fft21XuFQsHs7e3Zt99+qyprbGxk1tbWLCsrS2/90giOR48ePQIA2Nra8hyJYYmNjUVoaChGjRrFdygGpbCwEP7+/ggPD0evXr3Qv39/rF27lu+wDMawYcNw4MABVFZWAgDOnj2Lo0ePIiQkhOfIDE9VVRVqa2sRHBysKhMKhQgMDMRvv/2mt35oGT5PGGNISkrCsGHD4Ovry3c4BmPTpk34448/cPLkSb5DMTjXrl3DmjVrkJSUhAULFqCsrAxxcXEQCoWYPn063+F1evPmzcOjR4/g6ekJgUAAuVyO1NRUTJkyhe/QDE5tbS0AQCqVqpVLpVLcuHFDb/1QguPJnDlzcO7cORw9epTvUAxGdXU14uPjsW/fPohEIr7DMTgKhQL+/v5IS0sDAPTv3x8XL17EmjVrKMG9gfz8fOTm5mLjxo3w8fFBeXk5EhIS4OjoiBkzZvAdnkH653ZjjLF2bUHWGkpwPJg7dy4KCwtRWlqql213uorTp0+jrq4OAwcOVJXJ5XKUlpYiMzMTz58/h0Ag4DHCzs3BwQHe3t5qZV5eXigoKOApIsPy9ddfIzk5GZGRkQCA999/Hzdu3EB6ejoluDayt7cH0DKSc3BwUJXX1dVpjOp0Qdfg3iLGGObMmYNt27bh4MGDcHNz4zskgzJy5EicP38e5eXlqpe/vz+ioqJQXl5Oye01hg4dqrEspbKyEq6urjxFZFiePn2qscGmQCCgZQLt4ObmBnt7exQXF6vKmpqaUFJSgoCAAL31QyO4tyg2NhYbN27Ezp07IZFIVPPQ1tbWEIvFPEfX+UkkEo3rlZaWlrCzs6PrmG8gMTERAQEBSEtLQ0REBMrKypCdnY3s7Gy+QzMIYWFhSE1NhYuLC3x8fHDmzBmsWrUKMTExfIfWKT1+/BhXrlxRva+qqkJ5eTlsbW3h4uKChIQEpKWlwcPDAx4eHkhLS4OFhQWmTp2qvyD0dj8meS0AWl/r1q3jOzSDRcsE2mbXrl3M19eXCYVC5unpybKzs/kOyWDU19ez+Ph45uLiwkQiEXN3d2cLFy5kz58/5zu0TunQoUNav+9mzJjBGGtZKrBkyRJmb2/PhEIh+/DDD9n58+f1GgNtl0MIIcQo0TU4QgghRokSHCGEEKNECY4QQohRogRHCCHEKFGCI4QQYpQowRFCCDFKlOAIIYQYJUpwhBBCjBIlOELageM41ev48eOtttu8ebOq3bvvvqtWd/36da3l2lRUVIDjOEgkEjx9+vS17UeMGAGO43Ta723RokXgOA65ubntPgYhfKIER4iO8vLyWq3TV3Lw9vbGgAED8PjxYxQWFr6y7a1bt1BSUgKhUIjw8HC99E+IIaIER0g7CYVCeHt7Iz8/H83NzRr19+7dw969ezFgwAC99Ddt2jQAr0+aGzduhEKhwCeffAIbGxu99E2IIaIER4gOoqKiIJPJUFRUpFGXn5+Pv/76C9HR0Xrpa8qUKTA1NUVRURFkMlmr7ZQjSmVCJKSrogRHiA6ioqJavU6Vm5uLbt264dNPP9VLX1KpFKNHj0ZzczPy8/O1trl48SLOnj0LW1tbfPzxx6ryZ8+eIScnB+PGjYO7uztEIhFsbGwQFBSELVu2tCkOJycnmJpq32lr//794DgOn3/+uUadQqHAunXrMHz4cNUWUf369cN3332ndQQsk8mQnJwMb29vdOvWDTY2NujTpw9mzJiBU6dOtSlm0jVRgiNEB66urhg6dCgKCwvx+PFjVXlVVRWOHz+OCRMmwMLCQm/9KUdlrV33UybayZMnw9zcXFV+9epVfPHFFygrK4OLiwvGjx8PPz8/HDt2DBEREfjPf/6jtxi1kcvlCA8PR0xMDM6fP49BgwYhODgYd+7cQVJSEiZMmKC2cWh9fT0GDRqEjIwMyOVyjB07FiNHjoSVlRXy8vKwd+/eDo2XGAm9br5DSBcBgAmFQsYYY1lZWQwA27Bhg6p+2bJlDAArKipit2/fZgCYq6ur2jGqqqq0lr/K06dPmUQiYQDYlStX1OoUCgVzcXFhANixY8fU6urq6lhxcTGTy+Vq5VevXmXOzs5MIBCwmzdvqtUtXLiQAWA///yzWvk777zDBAKB1viKi4sZADZz5ky18tTUVAaAjR07lt29e1dVXl9fz0JCQhgAlpWVpSpfu3YtA8ASExM1+qitrWUXLlzQ2j8hL6MRHCE6ioiIgLm5udqoKi8vD/b29hg5cqRe+xKLxZg4cSKAlptJXnbkyBHcvHkT7u7uCAgIUKvr2bMnRo0aBRMT9f/y7u7uWLBgAeRyOX755Re9xqrU1NSElStXwtraGnl5eejRo4eqTiKRICcnB2ZmZvjhhx9U5XV1dQBaljv8k1QqhY+PT4fESoyL9ol0Qsgb6969O0JCQrBr1y7U1taiuroaly9fRmJiIgQCgd77mzZtGtavX4+8vDwsXrxYVa6cnnzVTS1HjhxBSUkJbt26hcbGRjDGUFNTAwD4888/9R4rAJw+fRr3799HWFgYbG1tNeodHBzw3nvv4dy5c2hqaoK5uTkGDhwIAEhOToaJiQlGjBgBkUjUIfER40UJjhA9iI6Oxo4dO7Bp0yZUVVWpyjpCUFAQnJyccPnyZZw6dQr+/v5oamrC1q1bW+334cOH+Oyzz3D48OFWj9vQ0NAh8V6/fh0AsGvXLnAc98q2Dx48gFQqxZgxYzB37lxkZmYiNDQUQqEQfn5+GDNmDGJiYuDq6tohsRLjQgmOED1Qrjn76aefUFNTAy8vL72tf/snExMTREVFISMjA7m5ufD398fu3bvx4MEDDB48GB4eHhqf+eqrr3D48GF89NFHSElJga+vL6ytrSEQCLBnzx6EhoaCMaZzbC/fKKIkl8sBAH369MGQIUNe+fmXb4xZvXo1Zs+ejR07duDgwYM4duwYTpw4gYyMDGzZsgVhYWE6x0uMGyU4QvRAKBRi0qRJyMnJAQDExcV1aH/Tpk1DRkYGNm3ahJUrV7527dv27dthamqKnTt3QiKRqNVdu3atTX2bm5tDLpejsbFRY9qwurpao72TkxMAoG/fvli/fn2b+vL09ERycjKSk5Px7NkzrF69GsnJyZg1axYlOPJadJMJIXoyffp02NnZoUePHoiKiurQvnx8fNC/f3/cuXMHBQUF2L17N8zMzDB58mSt7R8+fAgbGxuN5Aa0PC+zLRwcHAAAlZWVGnX79u3TKBs8eDAkEgkOHDigtpSircRiMebNm4eePXuipqYG9+/fb/exSNdACY4QPRk+fDhkMhnu3r37Vq4RKUdrs2fPRmNjI8aOHat2h+LLPDw8IJPJUFBQoFa+YsUKHDlypE39BgYGAgDS0tJU048AsGHDBq2LxsViMZKSknD//n1MnDhR6yjv7Nmzap/dtm0bysrKNNqdOnUKMpkM1tbWsLKyalPcpOuhBEeIgZo6dSoEAgHu3bsH4NU3tSxYsAAAEB4ejsDAQEyZMgVeXl6YP38+EhMT29Tv3LlzYWdnh/z8fPj4+CA8PBx9+/bFzJkzkZCQoPUzixcvRkREBPbt24fevXsjICAAkZGRGDVqFNzc3ODn56f2dJaDBw9i8ODBcHZ2xrhx4xAVFYWgoCAMGTIEjDGkpqa2+jQVQpQowRFioJSP7gIAKysrjBs3rtW206dPR2FhIQYNGoQzZ86gqKgILi4uOHToEEJCQtrUr4ODA0pLSxESEoKamhrs3bsXtra2OHDgQKvHEggEyM/Px+bNmxEYGIjKykps27YNFRUVcHBwwNKlS5Genq5qHxMTg6SkJNjb2+PEiRMoKCjA9evXERYWhpKSEsTGxrYpZtI1cUwft04RQgghnQyN4AghhBglSnCEEEKMEiU4QgghRokSHCGEEKNECY4QQohRogRHCCHEKFGCI4QQYpQowRFCCDFKlOAIIYQYJUpwhBBCjBIlOEIIIUaJEhwhhBCj9P/mFO6mT6CSSwAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 450x900 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.style.use('default')#('seaborn-notebook')\n", "\n", "colors = list(plt.rcParams['axes.prop_cycle'])\n", "col_idx = [2,4,5,1,3,0]\n", "\n", "ncols = 1\n", "nrows = 3\n", "fig, axs = plt.subplots(nrows, ncols, figsize=(4.5 * ncols, 3. * nrows))\n", "axs = np.ravel(axs)\n", "\n", "for i, model_name in enumerate(model_list):\n", "    plt.sca(axs[0])\n", "    plt.plot(mi_list, bias_dict[model_name], label=model_name, marker='d', color = colors[col_idx[i]][\"color\"]) \n", "    \n", "    plt.sca(axs[1])\n", "    plt.plot(mi_list, var_dict[model_name], label=model_name, marker='d', color = colors[col_idx[i]][\"color\"]) \n", "    \n", "    plt.sca(axs[2])\n", "    plt.plot(mi_list, mse_dict[model_name], label=model_name, marker='d', color = colors[col_idx[i]][\"color\"]) \n", "        \n", "ylabels = ['<PERSON><PERSON>', 'Variance', 'MSE']\n", "for i in range(3):\n", "    plt.sca(axs[i])\n", "    plt.ylabel(ylabels[i], fontsize=15)\n", "    \n", "    if i == 0:\n", "        if cubic:\n", "            plt.title('Cubic', fontsize=17)\n", "        else:\n", "            plt.title('<PERSON><PERSON>sian', fontsize=17)\n", "    if i == 1:\n", "        plt.yscale('log')\n", "    if i == 2:\n", "        plt.legend(loc='upper left', prop={'size': 12})\n", "        plt.xlabel('MI Values',fontsize=15)\n", "        \n", "plt.gcf().tight_layout()\n", "plt.savefig('bias_variance_Gaussian.pdf', bbox_inches='tight')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.12"}}, "nbformat": 4, "nbformat_minor": 4}