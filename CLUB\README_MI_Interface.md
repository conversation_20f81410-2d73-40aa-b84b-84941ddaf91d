# CLUB互信息估计接口

## 概述

本接口实现了CLUB (Contrastive Learning Upper Bound) 方法来估计互信息，适用于人工智能顶会论文研究。

### CLUB优化函数

**标准版本 (vCLUB):**
```
Î_vCLUB = (1/N)Σ[log q_θ(y_i|x_i) - (1/N)Σ log q_θ(y_j|x_i)]
```

**采样版本 (vCLUB-S):**
```
Î_vCLUB-S = (1/N)Σ[log q_θ(y_i|x_i) - log q_θ(y_k'|x_i)]
```

其中：
- `N` 是batch size
- `q_θ` 是变分网络编码器参数
- `(x_i, y_i)` 是正样本对
- `(x_i, y_j)` 或 `(x_i, y_k')` 是负样本对

## 快速开始

### 1. 基本用法

```python
from mi_estimators import MutualInformationEstimator
import torch

# 准备数据
x_samples = torch.randn(1000, 128)  # [N, x_dim]
y_samples = torch.randn(1000, 64)   # [N, y_dim]

# 初始化估计器
mi_estimator = MutualInformationEstimator(
    x_dim=128, 
    y_dim=64, 
    hidden_size=256,
    estimator_type='club'
)

# 训练变分网络
mi_estimator.train_variational_network(
    x_samples, y_samples, 
    epochs=100, 
    lr=1e-3
)

# 估计互信息
mi_value = mi_estimator.estimate_mi(x_samples, y_samples)
print(f"互信息估计: {mi_value:.4f} nats")
```

### 2. 分类变量

```python
# 连续X，分类Y
x_samples = torch.randn(1000, 128)
y_samples = torch.randint(0, 10, (1000,))  # 10个类别

mi_estimator = MutualInformationEstimator(
    x_dim=128,
    label_num=10,
    estimator_type='club_categorical'
)

mi_estimator.train_variational_network(x_samples, y_samples, epochs=100)
mi_value = mi_estimator.estimate_mi(x_samples, y_samples)
```

## API参考

### MutualInformationEstimator

#### 初始化参数

- `x_dim` (int): X变量的维度
- `y_dim` (int, 可选): Y变量的维度（连续变量）
- `label_num` (int, 可选): 分类标签数量（分类变量）
- `hidden_size` (int): 变分网络隐藏层大小，默认512
- `estimator_type` (str): 估计器类型
  - `'club'`: 标准CLUB估计器
  - `'club_mean'`: 固定方差的CLUB
  - `'club_sample'`: 采样版本CLUB
  - `'club_categorical'`: 分类变量CLUB

#### 主要方法

##### `train_variational_network(x_samples, y_samples, **kwargs)`
训练变分网络 q_θ(Y|X)

**参数:**
- `x_samples` (torch.Tensor): 输入样本，形状 [N, x_dim]
- `y_samples` (torch.Tensor): 目标样本，形状 [N, y_dim] 或 [N]（分类）
- `epochs` (int): 训练轮数，默认100
- `lr` (float): 学习率，默认1e-3
- `batch_size` (int): 批大小，默认None（全批次）
- `verbose` (bool): 是否显示训练进度，默认True

##### `estimate_mi(x_samples, y_samples)`
估计互信息 I(X,Y)

**参数:**
- `x_samples` (torch.Tensor): 输入样本
- `y_samples` (torch.Tensor): 目标样本

**返回:**
- `float`: 互信息估计值（nats）

##### `compute_log_likelihood(x_samples, y_samples)`
计算对数似然 log q_θ(Y|X)

##### `save_model(filepath)` / `load_model(filepath)`
保存/加载训练好的模型

## 支持的估计器类型

### 1. CLUB (标准版本)
- 使用高斯变分分布
- 同时学习均值和方差
- 适用于连续变量

### 2. CLUBMean
- 固定方差为1
- 只学习均值
- 计算更快，适合高维数据

### 3. CLUBSample
- 采样版本的CLUB
- 使用随机负样本
- 内存效率更高

### 4. CLUBForCategorical
- 专门用于分类变量
- 使用交叉熵损失
- 适合连续-分类变量对

## 实验建议

### 超参数调优
- **学习率**: 通常1e-3到1e-4效果较好
- **隐藏层大小**: 建议为输入维度的2-4倍
- **训练轮数**: 100-500轮，观察收敛情况
- **批大小**: 64-256，根据内存调整

### 数据预处理
- 标准化连续变量到零均值单位方差
- 确保数据没有NaN或无穷值
- 对于高维数据考虑降维

### 验证方法
- 使用已知MI的合成数据验证
- 比较不同估计器的结果
- 检查训练损失收敛情况

## 注意事项

1. **上界性质**: CLUB提供MI的上界估计，真实MI ≤ 估计值
2. **训练重要性**: 变分网络必须充分训练才能得到可靠估计
3. **维度诅咒**: 高维数据可能需要更大的网络和更多训练
4. **批大小影响**: 较大批大小通常给出更稳定的估计

## 引用

如果使用此接口，请引用CLUB原始论文：
```
@inproceedings{cheng2020club,
  title={CLUB: A contrastive log-ratio upper bound of mutual information},
  author={Cheng, Pengyu and Hao, Weituo and Dai, Shuyang and Liu, Jiachang and Gan, Zhe and Carin, Lawrence},
  booktitle={International Conference on Machine Learning},
  year={2020}
}
```
