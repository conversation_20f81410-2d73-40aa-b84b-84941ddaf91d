import sys
import time
import torch
import torch.nn.functional as F
from torch import tensor
from torch.optim import <PERSON>
from sklearn.model_selection import StratifiedKFold
from torch_geometric.data import DataLoader, DenseDataLoader as DenseLoader
# from confidence import ConfidenceEstmator
from utils import print_weights
import pathlib
import os
import numpy as np
from scipy.sparse import lil_matrix, save_npz
import scipy.sparse as sparse
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def parse_dataset_parm(dataset_name):
    class_dim = 0
    if dataset_name == "NCI1":
        class_dim = 2
    elif dataset_name == "NCI109":
        class_dim = 2

    return class_dim

def single_train_test(train_dataset,
                      test_dataset,
                      model_func,
                      epochs,
                      batch_size,
                      lr,
                      lr_decay_factor,
                      lr_decay_step_size,
                      weight_decay,
                      epoch_select,
                      with_eval_mode=True):
    assert epoch_select in ['test_last', 'test_max'], epoch_select

    model = model_func(train_dataset).to(device)
    print_weights(model)
    optimizer = Adam(model.parameters(), lr=lr, weight_decay=weight_decay)

    train_loader = DataLoader(train_dataset, batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size, shuffle=False)
    train_accs, test_accs = [], []
    t_start = time.perf_counter()
    for epoch in range(1, epochs + 1):
        if torch.cuda.is_available():
            torch.cuda.synchronize()

        train_loss, train_acc = train(
            model, optimizer, train_loader, device)
        train_accs.append(train_acc)
        test_accs.append(eval_acc(model, test_loader, device, with_eval_mode))

        if torch.cuda.is_available():
            torch.cuda.synchronize()

        print('Epoch: {:03d}, Train Acc: {:.4f}, Test Acc: {:.4f}'.format(
            epoch, train_accs[-1], test_accs[-1]))
        sys.stdout.flush()

        if epoch % lr_decay_step_size == 0:
            for param_group in optimizer.param_groups:
                param_group['lr'] = lr_decay_factor * param_group['lr']

    t_end = time.perf_counter()
    duration = t_end - t_start

    if epoch_select == 'test_max':
        train_acc = max(train_accs)
        test_acc = max(test_accs)
    else:
        train_acc = train_accs[-1]
        test_acc = test_accs[-1]

    return train_acc, test_acc, duration


from copy import deepcopy

def get_graph_feature(dataset,
                      model_func,
                      mutiview_model_func,
                      folds,
                      epochs,
                      batch_size,
                      lr,
                      lr_decay_factor,
                      lr_decay_step_size,
                      weight_decay,
                      epoch_select,
                      pretrain_check_size,
                      with_eval_mode=True,
                      logger=None,
                      dataset_name=None,
                      aug1=None, aug_ratio1=None,
                      aug2=None, aug_ratio2=None,
                      aug3=None, aug_ratio3=None,
                      aug4=None, aug_ratio4=None, suffix=None
                      ):
    assert epoch_select in ['val_max', 'test_max'], epoch_select
    val_losses, train_accs, test_accs, durations = [], [], [], []
    for fold, (train_idx, test_idx, val_idx) in enumerate(
            zip(*k_fold(dataset, folds, epoch_select))):
        """
        train_dataset = dataset[train_idx]
        test_dataset = dataset[test_idx]
        val_dataset = dataset[val_idx]

        train_loader = DataLoader(train_dataset, batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size, shuffle=False)
        test_loader = DataLoader(test_dataset, batch_size, shuffle=False)
        """
        dataset.aug = "none"

        # model = model_func(dataset).to(device)
        model_parameter = model_func(dataset).to(device)
        # mutiview_model_parameter = mutiview_model_func(dataset).to(device)
        # if fold == 0:
        #     # print_weights(model)
        #     print_weights(model_parameter)
        #     print_weights(mutiview_model_parameter)

        class_dim = parse_dataset_parm(dataset_name)
        # ce = ConfidenceEstmator(class_dim)
        # 原优化器
        optimizer = Adam(model_parameter.parameters(), lr=lr, weight_decay=weight_decay)
        # 共用一套GCN
        # 非共用一套GCN
        # optimizer = Adam([
        #     {'params': model_parameter.parameters(), 'lr': lr, 'weight_decay': weight_decay},
        #     {'params': mutiview_model_parameter.parameters(), 'lr': lr, 'weight_decay': weight_decay}])
        # if torch.cuda.is_available():
        #     torch.cuda.synchronize()

        # t_start = time.perf_counter()
        # pos_weights = np.array([[0.0 for i in range(class_dim)] for j in range(class_dim)])

        # with open('./logs/' + dataset_name + '_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(
        #         aug_ratio2) + '_ml_log', 'a+') as f:
        #     f.write('___________________________\n')
        #     f.write(str(dataset_name) + '_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(
        #         aug_ratio2) + aug3 + '_' + str(aug_ratio3) + '_' + aug4 + '_' + str(aug_ratio4) + '\n')

        # train_loss, _ = train(
        #     model, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2)

        train_loss,_ = get_feature_vec(
            model_parameter, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2)


        # for epoch in range(1, epochs + 1):

            # train_loss, _ = train(
            #     model, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2)

            # train_loss, _, pos_weights = trainML(
            #     model_parameter, model_func, mutiview_model_parameter, mutiview_model_func, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2, aug3, aug_ratio3, aug4, aug_ratio4, pretrain_check_size, class_dim, ce, epoch, pos_weights)
            # print("train_loss is :" + str(train_loss))

            # if epoch % lr_decay_step_size == 0:
            #     for param_group in optimizer.param_groups:
            #         param_group['lr'] = lr_decay_factor * param_group['lr']
            # log_path = './logs/' + dataset_name + '_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(
            #     aug_ratio2) + '_' + aug3 + '_' + str(aug_ratio3) + '_' + aug4 + '_' + str(aug_ratio4) + '_cl_log'
            # if (not os.path.exists(log_path)):
            #     pathlib.Path(log_path).touch()

            # with open(log_path, 'a+') as f:
            #     f.write(str(epoch) + ' ' + str(train_loss))
            #     f.write('\n')

            # original_modelfile_path = './models/' + dataset_name + '_original_' + aug1 + '_' + str(
            #     aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) + '_' + aug3 + '_' + str(
            #     aug_ratio3) + '_' + aug4 + '_' + str(aug_ratio4) + '_' + str(epoch) + '_' + str(lr) + '_' + str(
            #     suffix) + '.pt'
            # mutiview_modelfile_path = './models/' + dataset_name + '_mutiview_' + aug1 + '_' + str(
            #     aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) + '_' + aug3 + '_' + str(
            #     aug_ratio3) + '_' + aug4 + '_' + str(aug_ratio4) + '_' + str(epoch) + '_' + str(lr) + '_' + str(
            #     suffix) + '.pt'
            # if not os.path.exists(original_modelfile_path):
            #     pathlib.Path(original_modelfile_path).touch()
            #     pathlib.Path(mutiview_modelfile_path).touch()

            # if epoch % 20 == 0:
            #     torch.save(model_parameter.state_dict(), original_modelfile_path)
            #     torch.save(mutiview_model_parameter.state_dict(), original_modelfile_path)

        # print("finish run")
        # break

        if torch.cuda.is_available():
            torch.cuda.synchronize()

        t_end = time.perf_counter()
        durations.append(t_end - t_start)


def cross_validation_with_val_set(dataset,
                                  model_func,
                                  mutiview_model_func,
                                  folds,
                                  epochs,
                                  batch_size,
                                  lr,
                                  lr_decay_factor,
                                  lr_decay_step_size,
                                  weight_decay,
                                  epoch_select,
                                  pretrain_check_size,
                                  with_eval_mode=True,
                                  logger=None,
                                  dataset_name=None,
                                  aug1=None, aug_ratio1=None,
                                  aug2=None, aug_ratio2=None,
                                  aug3=None, aug_ratio3=None,
                                  aug4=None, aug_ratio4=None, suffix=None
                                  ):
    assert epoch_select in ['val_max', 'test_max'], epoch_select

    val_losses, train_accs, test_accs, durations = [], [], [], []
    for fold, (train_idx, test_idx, val_idx) in enumerate(
            zip(*k_fold(dataset, folds, epoch_select))):
        """
        train_dataset = dataset[train_idx]
        test_dataset = dataset[test_idx]
        val_dataset = dataset[val_idx]

        train_loader = DataLoader(train_dataset, batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size, shuffle=False)
        test_loader = DataLoader(test_dataset, batch_size, shuffle=False)
        """
        dataset.aug = "none"

        # model = model_func(dataset).to(device)
        model_parameter = model_func(dataset).to(device)
        mutiview_model_parameter = mutiview_model_func(dataset).to(device)
        if fold == 0:
            # print_weights(model)
            print_weights(model_parameter)
            print_weights(mutiview_model_parameter)

        class_dim = parse_dataset_parm(dataset_name)
        ce = ConfidenceEstmator(class_dim)
        # 原优化器
        # optimizer = Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
        # 共用一套GCN
        # 非共用一套GCN
        optimizer = Adam([
            {'params': model_parameter.parameters(), 'lr': lr, 'weight_decay': weight_decay},
            {'params': mutiview_model_parameter.parameters(), 'lr': lr, 'weight_decay': weight_decay}])
        if torch.cuda.is_available():
            torch.cuda.synchronize()

        t_start = time.perf_counter()
        pos_weights = np.array([[0.0 for i in range(class_dim)] for j in range(class_dim)])

        with open('./logs/' + dataset_name + '_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) + '_ml_log', 'a+') as f:
            f.write('___________________________\n')
            f.write(str(dataset_name) + '_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) +  aug3 + '_' + str(aug_ratio3) + '_' + aug4 + '_' + str(aug_ratio4) + '\n' )

        for epoch in range(1, epochs + 1):

            train_loss, _ = train(
                model, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2)

            # train_loss, _, pos_weights = trainML(
            #     model_parameter, model_func, mutiview_model_parameter, mutiview_model_func, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2, aug3, aug_ratio3, aug4, aug_ratio4, pretrain_check_size, class_dim, ce, epoch, pos_weights)
            print("train_loss is :" + str(train_loss))

            if epoch % lr_decay_step_size == 0:
                for param_group in optimizer.param_groups:
                    param_group['lr'] = lr_decay_factor * param_group['lr']
            log_path = './logs/' + dataset_name + '_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) + '_' + aug3 + '_' + str(aug_ratio3) + '_' + aug4 + '_' + str(aug_ratio4) + '_cl_log'
            if(not os.path.exists(log_path)):
                pathlib.Path(log_path).touch()

            with open(log_path, 'a+') as f:
                f.write(str(epoch) + ' ' + str(train_loss))
                f.write('\n')

            original_modelfile_path = './models/' + dataset_name + '_original_' + aug1 + '_' + str(aug_ratio1) + '_'+ aug2 + '_' + str(aug_ratio2) + '_' + aug3 + '_' + str(aug_ratio3) + '_'+ aug4 + '_' + str(aug_ratio4) + '_' + str(epoch) + '_' + str(lr) + '_' + str(suffix) + '.pt'
            mutiview_modelfile_path = './models/' + dataset_name + '_mutiview_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) + '_' + aug3 + '_' + str(aug_ratio3) + '_' + aug4 + '_' + str(aug_ratio4) + '_' + str(epoch) + '_' + str(lr) + '_' + str(suffix) + '.pt'
            if not os.path.exists(original_modelfile_path):
                pathlib.Path(original_modelfile_path).touch()
                pathlib.Path(mutiview_modelfile_path).touch()

            if epoch % 20 == 0:
                torch.save(model_parameter.state_dict(), original_modelfile_path)
                torch.save(mutiview_model_parameter.state_dict(), original_modelfile_path)

        print("finish run")
        break

        if torch.cuda.is_available():
            torch.cuda.synchronize()

        t_end = time.perf_counter()
        durations.append(t_end - t_start)

    """
    duration = tensor(durations)
    train_acc, test_acc = tensor(train_accs), tensor(test_accs)
    val_loss = tensor(val_losses)
    train_acc = train_acc.view(folds, epochs)
    test_acc = test_acc.view(folds, epochs)
    val_loss = val_loss.view(folds, epochs)
    if epoch_select == 'test_max':  # take epoch that yields best test results.
        _, selected_epoch = test_acc.mean(dim=0).max(dim=0)
        selected_epoch = selected_epoch.repeat(folds)
    else:  # take epoch that yields min val loss for each fold individually.
        _, selected_epoch = val_loss.min(dim=1)
    test_acc = test_acc[torch.arange(folds, dtype=torch.long), selected_epoch]
    train_acc_mean = train_acc[:, -1].mean().item()
    test_acc_mean = test_acc.mean().item()
    test_acc_std = test_acc.std().item()
    duration_mean = duration.mean().item()

    print('Train Acc: {:.4f}, Test Acc: {:.3f} ± {:.3f}, Duration: {:.3f}'.
          format(train_acc_mean, test_acc_mean, test_acc_std, duration_mean))
    sys.stdout.flush()

    return train_acc_mean, test_acc_mean, test_acc_std, duration_mean
    """


def k_fold(dataset, folds, epoch_select):
    skf = StratifiedKFold(folds, shuffle=True, random_state=12345)

    test_indices, train_indices = [], []
    for _, idx in skf.split(torch.zeros(len(dataset)), dataset.data.y):
        test_indices.append(torch.from_numpy(idx))

    if epoch_select == 'test_max':
        val_indices = [test_indices[i] for i in range(folds)]
    else:
        val_indices = [test_indices[i - 1] for i in range(folds)]

    for i in range(folds):
        train_mask = torch.ones(len(dataset), dtype=torch.uint8)
        train_mask[test_indices[i].long()] = 0
        train_mask[val_indices[i].long()] = 0
        train_indices.append(train_mask.nonzero().view(-1))

    return train_indices, test_indices, val_indices


def num_graphs(data):
    if data.batch is not None:
        return data.num_graphs
    else:
        return data.x.size(0)

def trainML(model_parameter, model_func, mutiview_model_parameter, mutiview_model_func, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2, aug3, aug_ratio3, aug4, aug_ratio4, pretrain_check_size, class_dim, confidence_estimator, epoch, pos_weights):
    #     , adjust_confience_interval = 10, confidence
    dataset.aug = "none"
    dataset_origin = dataset.shuffle()

    dataset1 = deepcopy(dataset_origin)
    dataset1.aug, dataset1.aug_ratio = aug1, aug_ratio1
    dataset2 = deepcopy(dataset_origin)
    dataset2.aug, dataset2.aug_ratio = aug2, aug_ratio2
    dataset3 = deepcopy(dataset_origin)
    dataset3.aug, dataset3.aug_ratio = aug3, aug_ratio3
    dataset4 = deepcopy(dataset_origin)
    dataset4.aug, dataset4.aug_ratio = aug4, aug_ratio4

    loader_origin = DataLoader(dataset_origin, batch_size, shuffle=False)
    loader1 = DataLoader(dataset1, batch_size, shuffle=False)
    loader2 = DataLoader(dataset2, batch_size, shuffle=False)
    loader3 = DataLoader(dataset1, batch_size, shuffle=False)
    loader4 = DataLoader(dataset2, batch_size, shuffle=False)

    mutiview_model_parameter.train()

    total_loss = 0
    index_data_traverse = 0
    # add Deep Metric Loss code here data: (B, D)
    for data_origin, data1, data2, data3, data4 in zip(loader_origin, loader1, loader2, loader3, loader4):
        # print(data1, data2)
        optimizer.zero_grad()
        data1 = data1.to(device)
        data2 = data2.to(device)
        data3 = data3.to(device)
        data4 = data4.to(device)

        # out1 = model_func.forward_cl(data1)
        # out2 = model_func.forward_cl(data2)
        # out3 = model_func.forward_cl(data3)
        # out4 = model_func.forward_cl(data4)

        data_origin = data_origin.to(device)
        out_origin = model_parameter.forward_ml(data_origin)

        list_mutiview_out = [data1, data2, data3, data4]
        out_mutiview = mutiview_model_parameter.forward(list_mutiview_out)

        # if epoch == 1 and index_data_traverse == 0:
        #     pos_weights = None
        # change into metric loss
        # try:
        loss = mutiview_model_parameter.loss_ml(out_mutiview, out_origin, pos_weights, epoch, index_data_traverse, pretrain_check_size, batch_size, data_origin)
        # except UnboundLocalError:
        #     print("epoch:" + str(epoch))
        #     print("index_data_traverse:" + str(index_data_traverse))

        loss.backward()
        # adjust confidence factor
        pos_weights = confidence_estimator.compute_weights(pretrain_check_size, batch_size, data_origin, list_mutiview_out, model_parameter, mutiview_model_parameter, class_dim)
        total_loss += loss.item() * num_graphs(data1)
        optimizer.step()
        index_data_traverse = index_data_traverse + 1
    return total_loss / len(loader1.dataset), 0, pos_weights


def get_feature_vec(model, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2):
    dataset1 = dataset.shuffle()
    graph_data = dataset.data
    # edge_index = graph_data.edge_index
    y = graph_data.y
    tmp_batch_size = graph_data.y.size(0)

    # dataset2 = deepcopy(dataset1)
    loader = DataLoader(dataset1, tmp_batch_size, shuffle=False)
    for data in loader:
        data = data.to(device)
        out = model.forward_cl(data)
        ndarryout = out.detach().numpy()
        [rows_nyout_index, cols_nyout_index] = ndarryout.shape

        # l = lil_matrix((6, 5))
        # l[2, 3] = 1
        # l[3, 4] = 2
        # l[3, 2] = 3
        # print(l.toarray())
        out_li = lil_matrix((rows_nyout_index, cols_nyout_index))
        # print(l.data)
        # print(l.rows)
        # out_li[2, 3] = 1
        # out_li[3, 4] = 2
        # out_li[3, 2] = 3
        for i in range(rows_nyout_index):
            for j in range(cols_nyout_index):
                out_li[i, j] = ndarryout[i][j]
        save_out_li_feature = sparse.csr_matrix(out_li)
        save_npz('./out_li_feature.npz', save_out_li_feature)
        break
    return

def train(model, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2):

    dataset.aug = "none"
    dataset1 = dataset.shuffle()
    dataset1.aug, dataset1.aug_ratio = aug1, aug_ratio1
    dataset2 = deepcopy(dataset1)
    dataset2.aug, dataset2.aug_ratio = aug2, aug_ratio2

    loader1 = DataLoader(dataset1, batch_size, shuffle=False)
    loader2 = DataLoader(dataset2, batch_size, shuffle=False)

    model.train()

    total_loss = 0
    correct = 0
    # add Deep Metric Loss code here data: (B, D)
    for data1, data2 in zip(loader1, loader2):
        # print(data1, data2)
        optimizer.zero_grad()
        data1 = data1.to(device)
        data2 = data2.to(device)
        out1 = model.forward_cl(data1)
        out2 = model.forward_cl(data2)

        loss = model.loss_cl(out1, out2)
        loss.backward()
        total_loss += loss.item() * num_graphs(data1)
        optimizer.step()
    return total_loss / len(loader1.dataset), 0


def compute_feature_vec_from_data(model, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2):
    dataset.aug = "none"
    dataset1 = dataset.shuffle()
    loader1 = DataLoader(dataset1, batch_size, shuffle=False)

    return

def eval_acc(model, loader, device, with_eval_mode):
    if with_eval_mode:
        model.eval()

    correct = 0
    for data in loader:
        data = data.to(device)
        with torch.no_grad():
            pred = model(data).max(1)[1]
        correct += pred.eq(data.y.view(-1)).sum().item()
    return correct / len(loader.dataset)


def eval_loss(model, loader, device, with_eval_mode):
    if with_eval_mode:
        model.eval()

    loss = 0
    for data in loader:
        data = data.to(device)
        with torch.no_grad():
            out = model(data)
        loss += F.nll_loss(out, data.y.view(-1), reduction='sum').item()
    return loss / len(loader.dataset)
