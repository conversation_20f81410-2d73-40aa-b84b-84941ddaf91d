import sys
import time
import torch
import torch.nn.functional as F
from torch import tensor
from torch.optim import Adam
from sklearn.model_selection import StratifiedKFold
from torch_geometric.data import DataLoader, DenseData<PERSON>oader as DenseLoader
from confidence import ConfidenceEstmator
from utils import print_weights
from models import DisentangledIBKD_GNN
from attention import GraphMLMutiView
import pathlib
import os
import numpy as np
from itertools import cycle


device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(device)
def parse_dataset_parm(dataset_name):
    class_dim = 0
    if dataset_name == "NCI1":
        class_dim = 2
    elif dataset_name == "NCI109":
        class_dim = 2
    elif dataset_name == "DD":
        class_dim = 2
    elif dataset_name == "FRANKENSTEIN":
        class_dim = 2
    elif dataset_name == "PTC_FM":
        class_dim = 2
    elif dataset_name == "ENZYMES":
        class_dim = 6
    elif dataset_name == "IMDB-BINARY":
        class_dim = 2
    elif dataset_name == "Mutagenicity":
        class_dim = 2
    elif dataset_name == "deezer_ego_nets":
        class_dim = 2
    elif dataset_name == "facebook_ct1":
        class_dim = 2
    elif dataset_name == "Tox21_MMP_training":
        class_dim = 2
    return class_dim

def single_train_test(train_dataset,
                      test_dataset,
                      model_func,
                      epochs,
                      batch_size,
                      lr,
                      lr_decay_factor,
                      lr_decay_step_size,
                      weight_decay,
                      epoch_select,
                      with_eval_mode=True):
    assert epoch_select in ['test_last', 'test_max'], epoch_select

    model = model_func(train_dataset).to(device)
    print_weights(model)
    optimizer = Adam(model.parameters(), lr=lr, weight_decay=weight_decay)

    train_loader = DataLoader(train_dataset, batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size, shuffle=False)
    train_accs, test_accs = [], []
    t_start = time.perf_counter()
    for epoch in range(1, epochs + 1):
        if torch.cuda.is_available():
            torch.cuda.synchronize()

        train_loss, train_acc = train(
            model, optimizer, train_loader, device)
        train_accs.append(train_acc)
        test_accs.append(eval_acc(model, test_loader, device, with_eval_mode))

        if torch.cuda.is_available():
            torch.cuda.synchronize()

        print('Epoch: {:03d}, Train Acc: {:.4f}, Test Acc: {:.4f}'.format(
            epoch, train_accs[-1], test_accs[-1]))
        sys.stdout.flush()

        if epoch % lr_decay_step_size == 0:
            for param_group in optimizer.param_groups:
                param_group['lr'] = lr_decay_factor * param_group['lr']

    t_end = time.perf_counter()
    duration = t_end - t_start

    if epoch_select == 'test_max':
        train_acc = max(train_accs)
        test_acc = max(test_accs)
    else:
        train_acc = train_accs[-1]
        test_acc = test_accs[-1]

    return train_acc, test_acc, duration


from copy import deepcopy
def cross_validation_with_val_set(source_dataset,
                                  target_dataset,
                                  model_func, # This will be ignored, but kept for compatibility
                                  mutiview_model_func, # This will be used to create the teacher
                                  folds,
                                  epochs,
                                  batch_size,
                                  lr,
                                  lr_decay_factor,
                                  lr_decay_step_size,
                                  weight_decay,
                                  epoch_select,
                                  pretrain_check_size,
                                  with_eval_mode=True,
                                  logger=None,
                                  source_dataset_name=None,
                                  target_dataset_name=None,
                                  aug1=None, aug_ratio1=None,
                                  aug2=None, aug_ratio2=None,
                                  aug3=None, aug_ratio3=None,
                                  aug4=None, aug_ratio4=None, suffix=None,
                                  carlibrator_switch=False,
                                  latent_dim=128,
                                  lambda_ib=1.0,
                                  lambda_r=1.0,
                                  lambda_kd=1.0,
                                  lambda_orth=1.0,
                                  beta_t=1.0,
                                  beta_s=1.0,
                                  gamma_s=1.0,
                                  kappa=1.0,
                                  redundancy_upper_bound=0.0):
    assert epoch_select in ['val_max', 'test_max'], epoch_select

    val_losses, train_accs, test_accs, durations = [], [], [], []
    for fold, (train_idx, test_idx, val_idx) in enumerate(
            zip(*k_fold(source_dataset, folds, epoch_select))):
        """
        train_dataset = dataset[train_idx]
        test_dataset = dataset[test_idx]
        val_dataset = dataset[val_idx]

        train_loader = DataLoader(train_dataset, batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size, shuffle=False)
        test_loader = DataLoader(test_dataset, batch_size, shuffle=False)
        """
        source_dataset.aug = "none"

        # 1. Initialize Teacher Model
        teacher_model = mutiview_model_func(source_dataset).to(device)

        # 2. Initialize the main DisentangledIBKD_GNN model
        source_class_dim = parse_dataset_parm(source_dataset_name)
        model = DisentangledIBKD_GNN(
            teacher_model=teacher_model,
            latent_dim=latent_dim,
            num_classes=source_class_dim,
            lambda_ib=lambda_ib,
            lambda_r=lambda_r,
            lambda_kd=lambda_kd,
            lambda_orth=lambda_orth
        ).to(device)

        if fold == 0:
            print_weights(model)

        optimizer = Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
        if torch.cuda.is_available():
            torch.cuda.synchronize()

        t_start = time.perf_counter()
        pos_weights = np.array([[0.0 for i in range(source_class_dim)] for j in range(source_class_dim)])

        # with open('./logs/' + dataset_name + '_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) +  aug3 + '_' + str(aug_ratio3) + aug4 + '_' + str(aug_ratio4)  + '_ml_log', 'a+') as f:
        #     f.write('___________________________\n')
        #     f.write(str(dataset_name) + '_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) +  aug3 + '_' + str(aug_ratio3) + '_' + aug4 + '_' + str(aug_ratio4) + '\n' )

        pos_weights_path = ""
        if(carlibrator_switch):
            pos_weights_path = './DrawPics/drifting_info/' + source_dataset_name + '_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) +  aug3 + '_' + str(aug_ratio3) + aug4 + '_' + str(aug_ratio4)  + '_posweight_log'
            if (not os.path.exists(pos_weights_path)):
                pathlib.Path(pos_weights_path).touch()
            with open(pos_weights_path, 'a+') as f:
                f.write('___________________________\n')
                f.write(str(source_dataset_name) + '_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) +  aug3 + '_' + str(aug_ratio3) + '_' + aug4 + '_' + str(aug_ratio4) + '\n' )
                f.write('~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n')

        train_dataset = source_dataset[train_idx]
        val_dataset = source_dataset[val_idx]
        test_dataset = source_dataset[test_idx]

        train_loader = DataLoader(train_dataset, batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size, shuffle=False)
        test_loader = DataLoader(test_dataset, batch_size, shuffle=False)

        if fold == 0:
            print('{} (Fold {}), Train: {}, Val: {}, Test: {}'.format(
                source_dataset_name, fold, len(train_dataset), len(val_dataset),
                len(test_dataset)))

        t_start = time.perf_counter()

        for epoch in range(1, epochs + 1):
            loss_dict = cross_domain_trainML(
                model, optimizer, train_dataset, target_dataset, device, batch_size,
                aug1, aug_ratio1, aug2, aug_ratio2, aug3, aug_ratio3, aug4, aug_ratio4,
                beta_t, beta_s, gamma_s, kappa, redundancy_upper_bound
            )
            train_loss = loss_dict['total_loss']
            print(f"Epoch {epoch:03d}, Train Loss: {train_loss:.4f}")

            if epoch % lr_decay_step_size == 0:
                for param_group in optimizer.param_groups:
                    param_group['lr'] = lr_decay_factor * param_group['lr']

            val_acc, _ = test(model, val_loader, device)
            test_acc, _ = test(model, test_loader, device)
            train_acc, _ = test(model, train_loader, device)

            val_losses.append(val_acc)
            test_accs.append(test_acc)
            train_accs.append(train_acc)

            if (carlibrator_switch):
                with open(pos_weights_path, 'a+') as f:
                    tmp_positive = "positive coefficient："
                    tmp_negative = "negative coefficient："
                    for i in range(source_class_dim):
                        tmp_positive = tmp_positive + str(pos_weights[i][i]) + " "
                        tmp_negative = tmp_negative + str(pos_weights[i][source_class_dim - i - 1]) + " "
                    tmp_positive = tmp_positive.strip()
                    tmp_negative = tmp_negative.strip()
                    f.write(tmp_positive + "\n")
                    f.write(tmp_negative + "\n")
                    f.write('~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n')
            original_modelfile_path = ""

            if carlibrator_switch:
                original_modelfile_path = './models/' + source_dataset_name + '_original_' + aug1 + '_' + \
                                          str(aug_ratio1) + '_'+ aug2 + '_' + str(aug_ratio2) + '_' + \
                                          aug3 + '_' + str(aug_ratio3) + '_'+ aug4 + '_' + str(aug_ratio4) + '_' + \
                                          str(epoch) + '_' + str(lr) + '_' + str(suffix) + '.pt'
            else:
                original_modelfile_path = './models/'+ 'no_carlibrator_' + source_dataset_name + \
                                          '_original_' + aug1 + '_' + str(aug_ratio1) + '_' \
                                          + aug2 + '_' + str(aug_ratio2) + '_' + aug3 + '_' \
                                          + str(aug_ratio3) + '_'+ aug4 + '_' + \
                                          str(aug_ratio4) + '_' + str(epoch) + '_' \
                                          + str(lr) + '_' + str(suffix) + '.pt'

            mutiview_modelfile_path = './models/' + source_dataset_name + '_multiview_' + aug1 + '_' + str(aug_ratio1) + '_' + aug2 + '_' + str(aug_ratio2) + '_' + aug3 + '_' + str(aug_ratio3) + '_' + aug4 + '_' + str(aug_ratio4) + '_' + str(epoch) + '_' + str(lr) + '_' + str(suffix) + '.pt'
            if not os.path.exists(original_modelfile_path):
                pathlib.Path(original_modelfile_path).touch()
                pathlib.Path(mutiview_modelfile_path).touch()

            if epoch % 20 == 0:
                torch.save(model.state_dict(), original_modelfile_path)
                torch.save(teacher_model.state_dict(), mutiview_modelfile_path)

        print("finish run")
        break

        if torch.cuda.is_available():
            torch.cuda.synchronize()

        t_end = time.perf_counter()
        durations.append(t_end - t_start)

    loss, acc = tensor(val_losses), tensor(test_accs)
    if epoch_select == 'test_max':
        acc = acc.view(folds, epochs)
        a = acc.max(dim=1)[0]
        acc = acc.max(dim=1)[0].mean().item()
    else: # val_max
        loss = loss.view(folds, epochs)
        best_epoch = loss.argmax(dim=1)
        acc = acc.view(folds, epochs)
        acc = acc[torch.arange(folds, dtype=torch.long), best_epoch].mean().item()

    print('Train Acc: {:.4f}, Test Acc: {:.3f}'.format(train_accs[-1], acc))
    sys.stdout.flush()

    return train_accs[-1], acc, 0, 0


def k_fold(dataset, folds, epoch_select):
    skf = StratifiedKFold(folds, shuffle=True, random_state=12345)

    test_indices, train_indices = [], []
    for _, idx in skf.split(torch.zeros(len(dataset)), dataset.data.y):
        test_indices.append(torch.from_numpy(idx))

    if epoch_select == 'test_max':
        val_indices = [test_indices[i] for i in range(folds)]
    else:
        val_indices = [test_indices[i - 1] for i in range(folds)]

    for i in range(folds):
        train_mask = torch.ones(len(dataset), dtype=torch.uint8)
        train_mask[test_indices[i].long()] = 0
        train_mask[val_indices[i].long()] = 0
        train_indices.append(train_mask.nonzero().view(-1))

    return train_indices, test_indices, val_indices


def num_graphs(data):
    if data.batch is not None:
        return data.num_graphs
    else:
        return data.x.size(0)

def cross_domain_trainML(model, optimizer, source_dataset, target_dataset, device, batch_size,
                         aug1, aug_ratio1, aug2, aug_ratio2, aug3, aug_ratio3, aug4, aug_ratio4,
                         beta_t=1.0, beta_s=1.0, gamma_s=1.0, kappa=1.0, R=0.0):
    
    source_dataset.aug = "none"
    dataset_origin = source_dataset.shuffle()

    # Create augmented views
    dataset1 = deepcopy(dataset_origin); dataset1.aug, dataset1.aug_ratio = aug1, aug_ratio1
    dataset2 = deepcopy(dataset_origin); dataset2.aug, dataset2.aug_ratio = aug2, aug_ratio2
    dataset3 = deepcopy(dataset_origin); dataset3.aug, dataset3.aug_ratio = aug3, aug_ratio3
    dataset4 = deepcopy(dataset_origin); dataset4.aug, dataset4.aug_ratio = aug4, aug_ratio4

    # Create DataLoaders
    loader_origin = DataLoader(dataset_origin, batch_size, shuffle=False)
    loader1 = DataLoader(dataset1, batch_size, shuffle=False)
    loader2 = DataLoader(dataset2, batch_size, shuffle=False)
    loader3 = DataLoader(dataset3, batch_size, shuffle=False)
    loader4 = DataLoader(dataset4, batch_size, shuffle=False)

    model.train()

    total_losses = {'total_loss': 0, 'loss_task': 0, 'loss_r': 0, 'loss_ib': 0, 'loss_kd': 0, 'loss_orth': 0}

    for data_origin, data1, data2, data3, data4 in zip(loader_origin, loader1, loader2, loader3, loader4):
        optimizer.zero_grad()

        data_origin = data_origin.to(device)
        labels = data_origin.y

        # Prepare list of augmented data for the model
        list_mutiview_data = [d.to(device) for d in [data1, data2, data3, data4]]

        # Forward pass
        outputs = model(list_mutiview_data)

        # Loss calculation with theoretical framework parameters
        loss_pack = model.loss_function(
            outputs, labels,
            beta_t=beta_t,
            beta_s=beta_s,
            gamma_s=gamma_s,
            kappa=kappa,
            R=R
        )
        loss = loss_pack['total_loss']

        loss.backward()
        optimizer.step()

        for key in total_losses:
            if loss_pack[key] is not None:
                total_losses[key] += loss_pack[key].item() * num_graphs(data_origin)
    
    num_samples = len(loader_origin.dataset)
    for key in total_losses:
        total_losses[key] /= num_samples

    return total_losses


def trainML(model_parameter, mutiview_model_parameter, \
             optimizer, dataset, device, batch_size, \
            aug1, aug_ratio1, aug2, aug_ratio2, aug3, aug_ratio3, aug4, aug_ratio4,\
            pretrain_check_size, class_dim, confidence_estimator, \
            epoch, pos_weights, carlibrator_switch):
    #     , adjust_confience_interval = 10, confidence
    dataset.aug = "none"
    dataset_origin = dataset.shuffle()

    dataset1 = deepcopy(dataset_origin)
    dataset1.aug, dataset1.aug_ratio = aug1, aug_ratio1
    dataset2 = deepcopy(dataset_origin)
    dataset2.aug, dataset2.aug_ratio = aug2, aug_ratio2
    dataset3 = deepcopy(dataset_origin)
    dataset3.aug, dataset3.aug_ratio = aug3, aug_ratio3
    dataset4 = deepcopy(dataset_origin)
    dataset4.aug, dataset4.aug_ratio = aug4, aug_ratio4

    loader_origin = DataLoader(dataset_origin, batch_size, shuffle=False)
    loader1 = DataLoader(dataset1, batch_size, shuffle=False)
    loader2 = DataLoader(dataset2, batch_size, shuffle=False)
    loader3 = DataLoader(dataset1, batch_size, shuffle=False)
    loader4 = DataLoader(dataset2, batch_size, shuffle=False)

    mutiview_model_parameter.train()

    total_loss = 0
    index_data_traverse = 0
    # add Deep Metric Loss code here data: (B, D)
    for data_origin, data1, data2, data3, data4 in zip(loader_origin, loader1, loader2, loader3, loader4):
        # print(data1, data2)
        optimizer.zero_grad()
        data1 = data1.to(device)
        data2 = data2.to(device)
        data3 = data3.to(device)
        data4 = data4.to(device)

        # out1 = model_func.forward_cl(data1)
        # out2 = model_func.forward_cl(data2)
        # out3 = model_func.forward_cl(data3)
        # out4 = model_func.forward_cl(data4)
        sys.stdout = open(os.devnull, 'w')

        data_origin = data_origin.to(device)
        out_origin = model_parameter.forward_ml(data_origin)

        list_mutiview_out = [data1, data2, data3, data4]
        out_mutiview = mutiview_model_parameter.forward(list_mutiview_out)

        # if epoch == 1 and index_data_traverse == 0:
        #     pos_weights = None
        # change into metric loss
        # try:
        loss = mutiview_model_parameter.loss_ml(out_mutiview, out_origin, \
        pos_weights, epoch, index_data_traverse, pretrain_check_size,\
        data_origin, carlibrator_switch)
        # except UnboundLocalError:
        #     print("epoch:" + str(epoch))
        #     print("index_data_traverse:" + str(index_data_traverse))
        sys.stdout = sys.__stdout__

        loss.backward()
        # adjust confidence factor
        pos_weights = confidence_estimator.compute_weights(pretrain_check_size, batch_size,\
        data_origin, list_mutiview_out, model_parameter, mutiview_model_parameter, class_dim)
        total_loss += loss.item() * num_graphs(data1)
        optimizer.step()
        index_data_traverse = index_data_traverse + 1
    return total_loss / len(loader1.dataset), 0


def train(model, optimizer, dataset, device, batch_size, aug1, aug_ratio1, aug2, aug_ratio2):

    dataset.aug = "none"
    dataset1 = dataset.shuffle()
    dataset1.aug, dataset1.aug_ratio = aug1, aug_ratio1
    dataset2 = deepcopy(dataset1)
    dataset2.aug, dataset2.aug_ratio = aug2, aug_ratio2

    loader1 = DataLoader(dataset1, batch_size, shuffle=False)
    loader2 = DataLoader(dataset2, batch_size, shuffle=False)

    model.train()

    total_loss = 0
    correct = 0
    # add Deep Metric Loss code here data: (B, D)
    for data1, data2 in zip(loader1, loader2):
        # print(data1, data2)
        optimizer.zero_grad()
        data1 = data1.to(device)
        data2 = data2.to(device)
        out1 = model.forward_cl(data1)
        out2 = model.forward_cl(data2)

        loss = model.loss_cl(out1, out2)
        loss.backward()
        total_loss += loss.item() * num_graphs(data1)
        optimizer.step()
    return total_loss / len(loader1.dataset), 0


def test(model, loader, device):
    model.eval()
    correct = 0
    total_graphs = 0

    # The model expects a list of views. For testing, we use the same graph for all views.
    # The number of views should match what the model was trained on.
    try:
        num_views = model.teacher.semantic_attention.num_view
    except AttributeError:
        # Handle cases where the model might not be the disentangled one, though it should be.
        num_views = 4 # Fallback to default

    with torch.no_grad():
        for data in loader:
            data = data.to(device)
            # Create a list of views for the model input
            data_list = [data for _ in range(num_views)]
            
            outputs = model(data_list)
            logits = outputs['logits']
            pred = logits.max(1)[1]
            
            correct += pred.eq(data.y).sum().item()
            total_graphs += data.num_graphs

    accuracy = correct / total_graphs if total_graphs > 0 else 0
    return accuracy, None  # Return accuracy and None for preds to maintain compatibility


def eval_acc(model, loader, device, with_eval_mode):
    if with_eval_mode:
        model.eval()

    correct = 0
    for data in loader:
        data = data.to(device)
        with torch.no_grad():
            pred = model(data).max(1)[1]
        correct += pred.eq(data.y.view(-1)).sum().item()
    return correct / len(loader.dataset)


def eval_loss(model, loader, device, with_eval_mode):
    if with_eval_mode:
        model.eval()

    loss = 0
    for data in loader:
        data = data.to(device)
        with torch.no_grad():
            out = model(data)
        loss += F.nll_loss(out, data.y.view(-1), reduction='sum').item()
    return loss / len(loader.dataset)
