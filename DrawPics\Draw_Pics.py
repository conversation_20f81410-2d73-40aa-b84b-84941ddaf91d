import matplotlib as mpl
import matplotlib.pyplot as plt
import numpy as np
import sys, os
import matplotlib.pyplot as plt
from mpl_toolkits.axes_grid1.inset_locator import mark_inset
from mpl_toolkits.axes_grid1.inset_locator import inset_axes

def parse_pic_log(dataset):
    pic_log_path = "./drift_info/" + dataset + '_dropN_0.2_maskN_0.2permE_0.2subgraph_0.2_posweight_log'

    # pic_log_path = "./drifting_info/" + dataset + ".txt"
    cnt = 0
    list_positive_00 = []
    list_positive_11 = []
    list_negative_01 = []
    list_negative_10 = []


    status_new_item = False
    sum_check = 0
    with open(pic_log_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if cnt != 0 and cnt != 1:
                if line == "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~":
                    status_new_item = True
                    # print(sum_check)
                    sum_check = 0
                else:
                    # if status_new_item:
                    #     status_new_item = False
                    #     str_arr_column = line.split("：")
                    #     print(str_arr_column)
                    str_arr_column = line.split("：")
                    # print(str_arr_column[1])
                    if str_arr_column[0] == "positive coefficient":
                        arr_positive = str_arr_column[1].split(" ")
                        list_positive_00.append(float(arr_positive[0]))
                        list_positive_11.append(float(arr_positive[1]))
                        sum_check = sum_check + float(arr_positive[0]) + float(arr_positive[1])
                    elif str_arr_column[0] == 'negative coefficient':
                        arr_negative = str_arr_column[1].split(" ")
                        list_negative_01.append(float(arr_negative[0]))
                        list_negative_10.append(float(arr_negative[1]))
                        sum_check = sum_check + float(arr_negative[0]) + float(arr_negative[1])
            cnt = cnt + 1
    return list_positive_00, list_positive_11, list_negative_01, list_negative_10
def show_local(dataset, ax, x, y):
    axins = inset_axes(ax, width="50%", height="30%", loc='center right',
                       borderpad=0,
                       bbox_to_anchor=(0, 0, 1, 1),
                       bbox_transform=ax.transAxes)
    axins.set_xlim(1, 101)
    ylim0 = 0.005
    ylim1= 0.2
    axins.set_ylim(ylim0, ylim1)
    mark_inset(ax, axins, loc1=3, loc2=1, fc="none", ec='k', lw=1)
    return axins
def draw_pic(dataset, list_positive_00, list_positive_11, list_negative_01, list_negative_10):
    caption_dic = {"NCI109":"NCI109","FRANKENSTEIN":"FRANK", "IMDB-BINARY":"IMDB", "DD":"DD", \
                   "Mutagenicity":"Mutagenicity", "deezer_ego_nets":"deezer" , \
                   "Tox21_MMP_training":"Tox21"}

    fig, ax = plt.subplots(figsize=(5, 4), layout='constrained')
    # x = np.linspace(1, 100, 100)
    x = [ i for i in range(0, 100)]
    positive_sum = []
    if (len(list_positive_00) != len(list_positive_11)):
        print("two posivie series length unequal!")
        return
    for i in range(len(list_positive_00)):
        tmp_positive_00 = list_positive_00[i]
        tmp_positive_11 = list_positive_11[i]
        tmp_positive_sum = tmp_positive_00 + tmp_positive_11
        positive_sum.append(tmp_positive_sum)
    ax.plot(x, list_positive_00, linestyle='-.', label=r"$\mathbf{E}_{\mathring{y} = 0,y^{\ast} =0 }$")
    ax.plot(x, list_positive_11, linestyle='dotted',label=r"$\mathbf{E}_{\mathring{y} = 1,y^{\ast} =1 }$")
    ax.plot(x, list_negative_01, linestyle='--', label=r"$\mathbf{E}_{\mathring{y} = 0,y^{\ast} =1 }$")
    ax.plot(x, list_negative_10, linestyle=':', label=r"$\mathbf{E}_{\mathring{y} = 1,y^{\ast} =0 }$")
    ax.plot(x, positive_sum, linestyle='solid', label=r"$\mathbf{E}_{\mathring{y} = 0,y^{\ast} =0 } + \mathbf{E}_{\mathring{y} = 1,y^{\ast} = 1 }$")
    # show_local(dataset, ax, x, list_negative_10)
    # fig.legend(loc='center right',prop = {'size':12})
    plt.legend(loc='right', prop={'size':12})
    # num1 = 1.05
    # num2 = 0.6
    # num3 = 3
    # num4 = 0
    # fig.legend(bbox_to_anchor=(num1, num2), loc=num3, borderaxespad=num4)

    # pic_title = caption_dic[dataset]
    # plt.title(pic_title)  # title 设置
    plt.xlabel("Nummber of Epochs")
    plt.ylabel("Graph Augmentation Estimation")
    fig.show()

    return

def draw_model_drifting_estimate(dataset):

    list_positive_00, list_positive_11, \
    list_negative_01, list_negative_10 = parse_pic_log(dataset)
    draw_pic(dataset, list_positive_00, list_positive_11, list_negative_01, \
             list_negative_10)

    # x = np.linspace(0, 2, 100)  # Sample data.

    # # Note that even in the OO-style, we use `.pyplot.figure` to create the Figure.
    # fig, ax = plt.subplots(figsize=(5, 2.7), layout='constrained')
    # ax.plot(x, x, label='linear')  # Plot some data on the axes.
    # ax.plot(x, x ** 2, label='quadratic')  # Plot more data on the axes...
    # ax.plot(x, x ** 3, label='cubic')  # ... and some more.
    # ax.set_xlabel('x label')  # Add an x-label to the axes.
    # ax.set_ylabel('y label')  # Add a y-label to the axes.
    # ax.set_title("Simple Plot")  # Add a title to the axes.
    # ax.legend()  # Add a legend.
    # fig.show()

    return
def show_local_loss(dataset, lst_epoch, lst_carlibrator_loss, lst_Nocarlibrator_loss, ax):

    axins_cal = inset_axes(ax, width="50%", height="45%", loc='center right',
                       borderpad=0,
                       bbox_to_anchor=(0, 0, 1, 1),
                       bbox_transform=ax.transAxes)

    # axins_no_cal = inset_axes(ax, width="30%", height="50%", loc='center right',
    #                    borderpad=0,
    #                    bbox_to_anchor=(0, 0, 1, 1),
    #                    bbox_transform=ax.transAxes)

    # ylim0 = -255
    # ylim1 = -215
    if dataset == "NCI109":
        axins_cal.set_xlim(0, 100)


        ylim0_cal = -251
        ylim1_cal = -250

        # axins_no_cal.set_xlim(0, 100)
        # ylim0_no_cal = -254
        # ylim1_no_cal = -253

    if dataset == "FRANKENSTEIN":
        axins_cal.set_xlim(0, 10)
        ylim0_cal = -256
        ylim1_cal = -247
    if dataset == "IMDB-BINARY":
        axins_cal.set_xlim(0, 10)
        ylim0_cal = -255
        ylim1_cal = -240
    if dataset == "DD":
        axins_cal.set_xlim(0, 10)
        ylim0_cal = -252
        ylim1_cal = -235
    if dataset == "Mutagenicity":
        axins_cal.set_xlim(0, 10)
        ylim0_cal = -255
        ylim1_cal = -248
    if dataset == "deezer_ego_nets":
        axins_cal.set_xlim(0, 40)
        ylim0_cal = -254.5
        ylim1_cal =  -251
    if dataset == "Tox21_MMP_training":
        # axins.set_xlim(0, 3)
        # ylim0 = -255
        # ylim1 = -230

        axins_cal.set_xlim(0, 10)
        ylim0_cal = -255
        ylim1_cal = -250

        # axins_no_cal.set_xlim(0, 100)
        ylim0_no_cal = -255
        ylim1_no_cal = -254

    axins_cal.set_ylim(ylim0_cal, ylim1_cal)
    # axins_no_cal.set_ylim(ylim0_no_cal, ylim1_no_cal)

    axins_cal.plot(lst_epoch, lst_carlibrator_loss, color='blue', \
               linewidth=0.5, marker="o", markersize=0.5, alpha=0.7)
    axins_cal.plot(lst_epoch, lst_Nocarlibrator_loss, color='brown', \
               linewidth=0.5, marker="^", markersize=0.5,  alpha=0.7)

    # axins_no_cal.plot(lst_epoch, lst_Nocarlibrator_loss, color='brown', \
    #            linewidth=0.5, marker="^", markersize=0.5,  alpha=0.7)

    mark_inset(ax, axins_cal, loc1=2, loc2=4, fc="none", ec='k', lw=1)
    # mark_inset(ax, axins_no_cal, loc1=2, loc2=4, fc="none", ec='k', lw=1)

    return axins_cal
def draw_epoch_loss(dataset):
    lr = 0.001
    loss_carlibrator_log_path = "./loss_info/" + dataset +  "/" + dataset + \
                    '_' + str(lr) + '_' + 'calibrator_True' + \
                                '_dropN_0.2_maskN_0.2_permE_0.2_subgraph_0.2_ml_log'
    loss_Nocarlibrator_log_path = "./loss_info/" + dataset +  "/" + dataset + \
                    '_' + str(lr) + '_' + 'calibrator_False' + \
                                '_dropN_0.2_maskN_0.2_permE_0.2_subgraph_0.2_ml_log'

    lst_epoch = []
    lst_carlibrator_loss = []
    lst_Nocarlibrator_loss = []

    with open(loss_carlibrator_log_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            arr_line = line.split(" ")
            lst_epoch.append(int(arr_line[0]))
            lst_carlibrator_loss.append(float(arr_line[1]))

    with open(loss_Nocarlibrator_log_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            arr_line = line.split(" ")
            lst_Nocarlibrator_loss.append(float(arr_line[1]))


    fig, ax = plt.subplots(figsize=(5, 4), layout='constrained')
    ax.plot(lst_epoch, lst_carlibrator_loss, linewidth=1, marker="o", \
            markersize=0.1, linestyle='solid', color='blue', \
            label=r"DMG with Graph Aug. Calibrator")
    ax.plot(lst_epoch, lst_Nocarlibrator_loss, linewidth=1, marker="^", \
            markersize=0.1, linestyle='solid', color='brown', \
            label=r"DMG without Graph Aug. Calibrator")


    show_local_loss(dataset, lst_epoch, lst_carlibrator_loss, lst_Nocarlibrator_loss, ax)

    ax.set_xlabel("Nummber of Epochs")
    ax.set_ylabel("Deep Metric Loss")
    ax.legend(loc='upper right', prop={'size':12})

    fig.show()
    return
if __name__ == '__main__':
    # dataset = "NCI109"
    # dataset = "FRANKENSTEIN"
    dataset = "IMDB-BINARY"
    # dataset = "DD"
    # dataset = "Mutagenicity"
    # dataset = "deezer_ego_nets"
    # dataset = "Tox21_MMP_training"
    # draw_model_drifting_estimate(dataset)
    draw_epoch_loss(dataset)

