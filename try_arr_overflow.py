import torch
from res_gcn import ResGC<PERSON>

from datasets import get_dataset
import argparse


arr_0 = torch.ones(5, 2)
arr_1 = arr_0[:7,:]
print (arr_1.shape)
dim_2 = arr_1.shape[1]
print(dim_2)
# tmp_path =
arr_2 = arr_0 + 1
log_arr_0 = torch.log(arr_0)
print(log_arr_0)


def get_model_with_default_configs(model_name,
                                   num_feat_layers=1,
                                   num_conv_layers=3,
                                   num_fc_layers=2,
                                   residual=False,
                                   hidden=128):
    # More default settings.
    res_branch = 'BNConvReLU'
    global_pool = 'sum'
    dropout = 0
    edge_norm = True

    # modify default architecture when needed


    if model_name.startswith('ResGFN'):
        collapse = True if 'flat' in model_name else False
        def foo(dataset):
            return ResGCN(dataset, hidden, num_feat_layers, num_conv_layers,
                          num_fc_layers, gfn=True, collapse=collapse,
                          residual=residual, res_branch=res_branch,
                          global_pool=global_pool, dropout=dropout,
                          edge_norm=edge_norm)
    elif model_name.startswith('ResGCN'):
        def foo(dataset):
            return ResGCN(dataset, hidden, num_feat_layers, num_conv_layers,
                          num_fc_layers, gfn=False, collapse=False,
                          residual=residual, res_branch=res_branch,
                          global_pool=global_pool, dropout=dropout,
                          edge_norm=edge_norm)
    return foo


model_PATH = "./models/NCI109_original_dropN_0.2_maskN_0.2_permE_0.2_subgraph_0.2_100_1e-08_0.pt"
# model_func = get_model_with_default_configs("ResGCN")
# model = model_func()
model_func = get_model_with_default_configs("ResGCN")
dataset_name = "NCI109"
dataset = get_dataset(dataset_name, sparse=True, feat_str='deg+odeg100', root=None)

model = model_func(dataset)


model.load_state_dict(torch.load(model_PATH, map_location=torch.device('cpu')), strict=False)
parser = argparse.ArgumentParser()

# os.path.exists()


